import{d as W,r as m,a as N,c as A,b as e,w as t,h as s,A as X,c4 as Y,E as g,e as ee,C as te,o as y,f as x,i,T as ae,I as O,U as oe,c5 as re,c6 as le,c7 as se,p as ne,l as ie,q as de,s as ue,N as ce,ae as pe,af as me,v as ge,x as _e,y as fe,k as ye,a3 as ve,al as he,am as be,Z as we,_ as Ce}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                        *//* empty css                     */const xe={class:"search-box"},Ee={key:1},Ve={class:"pagination-container"},Ne={class:"dialog-footer"},Ae=W({__name:"ClassListView",setup(Oe){const v=m(0),h=m(!1),n=N({merchantGuid:"",pageSize:10,page:1});let b=m([]);const u=m(!1),w=m(""),C=m(),_=m("add"),r=N({guid:"",merchantGuid:"",categoryName:"",sortOrder:1,status:1}),k={merchantGuid:[{required:!0,message:"请选择商户",trigger:"change"}],categoryName:[{required:!0,message:"请输入分类名称",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},c=async()=>{h.value=!0;try{let a=await Y(n);a.data&&Array.isArray(a.data)?(b.value=a.data,v.value=a.data.length):a.data&&a.data.data&&(b.value=a.data.data,v.value=a.data.total)}catch{g.error("获取分类列表失败")}finally{h.value=!1}},z=()=>{n.page=1,c()},I=()=>{n.page=1,c()},S=a=>{n.page=a,c()},G=a=>{n.pageSize=a,n.page=1,c()},T=()=>{_.value="add",w.value="新增分类",Object.assign(r,{guid:"",merchantGuid:"",categoryName:"",sortOrder:1,status:1}),u.value=!0},D=a=>{_.value="edit",w.value="编辑分类",Object.assign(r,{guid:a.guid,merchantGuid:a.merchantGuid,categoryName:a.categoryName,sortOrder:a.sortOrder,status:a.status}),u.value=!0},U=async a=>{try{await re({guid:a.guid}),g.success("删除成功"),c()}catch{g.error("删除失败")}},L=async()=>{C.value&&await C.value.validate(async a=>{if(a)try{_.value==="edit"?(await le({guid:r.guid,categoryName:r.categoryName,sortOrder:r.sortOrder,status:r.status}),g.success("更新成功")):(await se({merchantGuid:r.merchantGuid,categoryName:r.categoryName,sortOrder:r.sortOrder,status:r.status}),g.success("创建成功")),u.value=!1,c()}catch{g.error(_.value==="edit"?"更新失败":"创建失败")}})};return c(),(a,l)=>{const p=ne,f=ie,E=de,d=ue,R=ce,q=pe,B=me,P=ge,j=_e,$=fe,F=ee,M=ye,Z=ve,V=he,H=be,J=te,K=we;return y(),A("div",null,[e(F,{class:"wrapper"},{default:t(()=>[e($,null,{default:t(()=>[x("div",xe,[e(E,{inline:!0,class:"search-form"},{default:t(()=>[e(f,null,{default:t(()=>[e(p,{type:"primary",onClick:z},{default:t(()=>[i("搜索")]),_:1}),e(p,{onClick:I},{default:t(()=>[i("重置")]),_:1}),e(p,{type:"success",onClick:T},{default:t(()=>[i("新增分类")]),_:1})]),_:1})]),_:1})]),ae((y(),O(P,{data:s(b),border:"",style:{width:"100%"}},{default:t(()=>[e(d,{prop:"sysId",label:"ID"}),e(d,{prop:"categoryName",label:"分类名称"}),e(d,{prop:"categoryDesc",label:"分类描述","show-overflow-tooltip":""}),e(d,{prop:"categoryIcon",label:"分类图标",width:"100"},{default:t(o=>[o.row.categoryIcon?(y(),O(R,{key:0,src:o.row.categoryIcon,style:{width:"40px",height:"40px"},fit:"cover"},null,8,["src"])):(y(),A("span",Ee,"-"))]),_:1}),e(d,{prop:"sortOrder",label:"排序"}),e(d,{prop:"status",label:"状态",width:"100"},{default:t(o=>[e(q,{type:o.row.status===1?"success":"danger"},{default:t(()=>[i(oe(o.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"createTime",label:"创建时间"}),e(d,{fixed:"right",label:"操作",width:"150"},{default:t(o=>[e(p,{size:"small",type:"primary",onClick:Q=>D(o.row)},{default:t(()=>[i("编辑")]),_:2},1032,["onClick"]),e(B,{title:"确认删除该分类吗？","confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:Q=>U(o.row)},{reference:t(()=>[e(p,{size:"small",type:"danger"},{default:t(()=>[i("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[K,s(h)]]),x("div",Ve,[e(j,{"current-page":s(n).page,"onUpdate:currentPage":l[0]||(l[0]=o=>s(n).page=o),"page-size":s(n).pageSize,"onUpdate:pageSize":l[1]||(l[1]=o=>s(n).pageSize=o),"page-sizes":[10,20,50,100],total:s(v),layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页",onSizeChange:G,onCurrentChange:S},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(J,{title:s(w),modelValue:s(u),"onUpdate:modelValue":l[6]||(l[6]=o=>X(u)?u.value=o:null),width:"600px"},{footer:t(()=>[x("span",Ne,[e(p,{onClick:l[5]||(l[5]=o=>u.value=!1)},{default:t(()=>[i("取消")]),_:1}),e(p,{type:"primary",onClick:L},{default:t(()=>[i("确定")]),_:1})])]),default:t(()=>[e(E,{ref_key:"formRef",ref:C,model:s(r),rules:k,"label-width":"100px"},{default:t(()=>[e(f,{label:"分类名称",prop:"categoryName"},{default:t(()=>[e(M,{modelValue:s(r).categoryName,"onUpdate:modelValue":l[2]||(l[2]=o=>s(r).categoryName=o),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"排序",prop:"sortOrder"},{default:t(()=>[e(Z,{modelValue:s(r).sortOrder,"onUpdate:modelValue":l[3]||(l[3]=o=>s(r).sortOrder=o),min:0,max:999,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(f,{label:"状态",prop:"status"},{default:t(()=>[e(H,{modelValue:s(r).status,"onUpdate:modelValue":l[4]||(l[4]=o=>s(r).status=o)},{default:t(()=>[e(V,{label:1},{default:t(()=>[i("启用")]),_:1}),e(V,{label:2},{default:t(()=>[i("禁用")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});const Fe=Ce(Ae,[["__scopeId","data-v-111974e3"]]);export{Fe as default};
