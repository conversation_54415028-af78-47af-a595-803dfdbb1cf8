import{d as be,r as c,a as E,by as ye,c as S,b as a,w as l,h as o,A as L,cp as we,E as P,e as Ve,C as ke,o as _,T as Ge,I as b,f as s,G as Y,H as O,aC as Ce,U as d,i as u,F as G,cq as Te,k as Ae,l as Ee,X as Se,p as xe,q as Fe,s as Ne,ci as De,ae as Ie,v as Ue,y as ze,x as qe,Y as Le,m as Pe,n as Be,al as Me,am as Re,a3 as Ye,Z as Oe,bb as He,bc as je,c8 as $e,_ as Xe}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                        *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                  *//* empty css                     *//* empty css                       */const x=C=>(He("data-v-de55df2b"),<PERSON>=C(),je(),C),Ze={class:"header-box"},Je={class:"mode-box"},Ke=["onClick"],Qe={class:"agent-info"},We={class:"agent-details"},ea={class:"agent-name"},aa={class:"agent-price"},ta={class:"user-info"},la={class:"user-details"},oa={class:"mobile"},na={class:"creator-info"},sa={class:"creator-name"},ra={class:"amount-info"},ia={class:"pay-amount"},da={class:"platform-fee"},ua={class:"creator-income"},ca={class:"agent-option"},ma={class:"agent-name"},_a={class:"agent-price"},pa={class:"agent-preview"},ga={class:"agent-detail"},fa=x(()=>s("strong",null,"智能体名称：",-1)),va=x(()=>s("strong",null,"智能体价格：",-1)),ha=x(()=>s("strong",null,"是否精品：",-1)),ba=x(()=>s("strong",null,"创作者：",-1)),ya={class:"dialog-footer"},wa=be({__name:"BoutiqueVipListView",setup(C){const y=c(!1),T=c(0),F=c(""),A=c(null);c(!1),c(null);const w=c(!1),N=c(!1),V=c(),f=c("userGuid"),v=c(null),i=E({merchantGuid:"",agentGuid:"",userGuid:"",nickname:"",actualPayAmount:0}),H={packageGuid:[{required:!0,message:"请选择套餐",trigger:"change"}],userGuid:[{required:!0,message:"请输入用户GUID",trigger:"blur",validator:(t,n,p)=>{f.value==="userGuid"&&!n?p(new Error("请输入用户GUID")):p()}}],nickname:[{required:!0,message:"请输入用户昵称",trigger:"blur",validator:(t,n,p)=>{f.value==="nickname"&&!n?p(new Error("请输入用户昵称")):p()}}]},j=E([{name:"全部",value:""},{name:"待支付",value:100},{name:"已支付",value:200},{name:"取消支付",value:300},{name:"支付超时",value:400},{name:"已退款",value:500}]),r=E({merchantGuid:"",orderStatus:"",agentName:"",orderNo:"",userMobile:"",startTime:"",endTime:"",page:1,limit:20}),D=c([]),$=t=>{switch(t){case 100:return"warning";case 200:return"success";case 300:return"info";case 400:return"danger";case 500:return"primary";default:return"info"}},X=t=>{F.value=t.toString(),r.orderStatus=t,r.page=1,h()},Z=t=>{t?(r.startTime=t[0],r.endTime=t[1]):(r.startTime="",r.endTime="")},h=async()=>{try{y.value=!0;const t=await we(r);t.data&&(T.value=t.data.total||0,D.value=t.data.data||[])}catch(t){console.error("获取订单列表失败:",t),P.error("获取订单列表失败"),D.value=[],T.value=0}finally{y.value=!1}},J=()=>{r.page=1,h()},K=()=>{Object.assign(r,{orderStatus:"",agentName:"",orderNo:"",userMobile:"",startTime:"",endTime:"",page:1,limit:20}),F.value="",A.value=null,h()},Q=t=>{r.page=t,h()},W=t=>{r.limit=t,r.page=1,h()},ee=E({merchantGuid:"",categoryGuid:"",agentType:"",auditStatus:"",status:"",isPaid:"",agentName:"",creatorNickname:"",pageSize:300,page:1});let I=c([]);const ae=async()=>{y.value=!0;try{let t=await $e(ee);t.data&&t.data.data&&(I.value=t.data.data.filter(n=>n.isFeatured===1),T.value=t.data.total)}catch{P.error("获取智能体列表失败")}finally{y.value=!1}},te=()=>{w.value=!0,ae(),B()},B=()=>{var t;Object.assign(i,{merchantGuid:"",agentGuid:"",userGuid:"",nickname:"",actualPayAmount:0}),f.value="userGuid",v.value=null,(t=V.value)==null||t.clearValidate()},le=t=>{v.value=I.value.find(n=>n.guid===t)||null},oe=t=>{var n;t==="userGuid"?i.nickname="":i.userGuid="",(n=V.value)==null||n.clearValidate()},U=()=>{w.value=!1,B()},ne=async()=>{if(V.value)try{await V.value.validate(),N.value=!0;const t={agentGuid:i.agentGuid,actualPayAmount:i.actualPayAmount};f.value==="userGuid"?t.userGuid=i.userGuid:t.nickname=i.nickname;const{data:n}=await Te(t);n.data?(U(),h()):P.warning(n.message)}catch(t){console.error("添加会员失败:",t)}finally{N.value=!1}};return ye(()=>{h()}),(t,n)=>{const p=Ae,m=Ee,se=Se,k=xe,M=Fe,g=Ne,z=De,q=Ie,re=Ue,ie=ze,de=qe,ue=Le,ce=Ve,me=Pe,_e=Be,R=Me,pe=Re,ge=Ye,fe=ke,ve=Oe;return _(),S("div",null,[a(ce,{class:"wrapper"},{default:l(()=>[Ge((_(),b(ie,null,{default:l(()=>[s("div",Ze,[a(M,{inline:!0,model:o(r),class:"demo-form-inline"},{default:l(()=>[a(m,{label:"订单编号"},{default:l(()=>[a(p,{modelValue:o(r).orderNo,"onUpdate:modelValue":n[0]||(n[0]=e=>o(r).orderNo=e),placeholder:"请输入订单编号",clearable:""},null,8,["modelValue"])]),_:1}),a(m,{label:"订单状态"},{default:l(()=>[s("div",Je,[(_(!0),S(Y,null,O(o(j),(e,he)=>(_(),S("div",{class:Ce(["item",{active:o(F)===e.value}]),key:he,onClick:Va=>X(e.value)},d(e.name),11,Ke))),128))])]),_:1}),a(m,{label:"智能体名称"},{default:l(()=>[a(p,{modelValue:o(r).agentName,"onUpdate:modelValue":n[1]||(n[1]=e=>o(r).agentName=e),placeholder:"请输入智能体名称",clearable:""},null,8,["modelValue"])]),_:1}),a(m,{label:"时间范围"},{default:l(()=>[a(se,{modelValue:o(A),"onUpdate:modelValue":n[2]||(n[2]=e=>L(A)?A.value=e:null),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:Z},null,8,["modelValue"])]),_:1}),a(m,null,{default:l(()=>[a(k,{type:"primary",onClick:J},{default:l(()=>[u("搜索")]),_:1}),a(k,{onClick:K},{default:l(()=>[u("重置")]),_:1}),a(k,{type:"success",onClick:te},{default:l(()=>[u("新增会员")]),_:1})]),_:1})]),_:1},8,["model"])]),a(re,{data:o(D),border:"",style:{width:"100%"}},{default:l(()=>[a(g,{prop:"sysId",label:"订单ID",width:"80"}),a(g,{label:"智能体信息",width:"200"},{default:l(e=>[s("div",Qe,[a(z,{src:e.row.agent.agentAvatar,size:30},null,8,["src"]),s("div",We,[s("div",ea,d(e.row.agent.agentName),1),s("div",aa,"¥"+d((e.row.agent.price/100).toFixed(2)),1),e.row.agent.isFeatured===1?(_(),b(q,{key:0,type:"warning",size:"small"},{default:l(()=>[u("精品")]),_:1})):G("",!0)])])]),_:1}),a(g,{label:"购买用户",width:"180"},{default:l(e=>[s("div",ta,[a(z,{src:e.row.buyer.headImgurl,size:30},null,8,["src"]),s("div",la,[s("div",null,d(e.row.buyer.nickname),1),s("div",oa,d(e.row.buyer.mobile),1)])])]),_:1}),a(g,{label:"智能体创作者",width:"150"},{default:l(e=>[s("div",na,[a(z,{src:e.row.agentOwner.headImgurl,size:24},null,8,["src"]),s("span",sa,d(e.row.agentOwner.nickname),1)])]),_:1}),a(g,{prop:"orderNo",label:"订单编号","min-width":"180"}),a(g,{label:"金额信息",width:"150"},{default:l(e=>[s("div",ra,[s("div",ia,d(e.row.payAmountText),1),s("div",da,"平台费: "+d(e.row.platformFeeText),1),s("div",ua,"创作者收入: "+d(e.row.creatorIncomeText),1)])]),_:1}),a(g,{prop:"orderStatusText",label:"订单状态",width:"100"},{default:l(e=>[a(q,{type:$(e.row.orderStatus),size:"small"},{default:l(()=>[u(d(e.row.orderStatusText),1)]),_:2},1032,["type"])]),_:1}),a(g,{prop:"createTime",label:"下单时间",width:"160"}),a(g,{prop:"payTime",label:"支付时间",width:"160"}),a(g,{prop:"expireTime",label:"到期时间",width:"160"})]),_:1},8,["data"])]),_:1})),[[ve,o(y)]]),a(ue,null,{default:l(()=>[a(de,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:o(T),"current-page":o(r).page,"page-size":o(r).limit,"page-sizes":[10,20,50,100],onCurrentChange:Q,onSizeChange:W},null,8,["total","current-page","page-size"])]),_:1})]),_:1}),a(fe,{modelValue:o(w),"onUpdate:modelValue":n[8]||(n[8]=e=>L(w)?w.value=e:null),title:"新增精品智能体会员",width:"600px","before-close":U},{footer:l(()=>[s("span",ya,[a(k,{onClick:U},{default:l(()=>[u("取消")]),_:1}),a(k,{type:"primary",onClick:ne,loading:o(N)},{default:l(()=>[u("确认添加")]),_:1},8,["loading"])])]),default:l(()=>[a(M,{model:o(i),rules:H,ref_key:"addFormRef",ref:V,"label-width":"120px"},{default:l(()=>[a(m,{label:"精品智能体",prop:"agentGuid",required:""},{default:l(()=>[a(_e,{modelValue:o(i).agentGuid,"onUpdate:modelValue":n[3]||(n[3]=e=>o(i).agentGuid=e),placeholder:"请选择精品智能体",style:{width:"100%"},onChange:le},{default:l(()=>[(_(!0),S(Y,null,O(o(I),e=>(_(),b(me,{key:e.guid,label:`${e.agentName} - ¥${(e.price/100).toFixed(2)}`,value:e.guid},{default:l(()=>[s("div",ca,[s("span",ma,d(e.agentName),1),s("span",_a,"¥"+d((e.price/100).toFixed(2)),1),e.isFeatured===1?(_(),b(q,{key:0,type:"warning",size:"small"},{default:l(()=>[u("精品")]),_:1})):G("",!0)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"用户选择",required:""},{default:l(()=>[a(pe,{modelValue:o(f),"onUpdate:modelValue":n[4]||(n[4]=e=>L(f)?f.value=e:null),onChange:oe},{default:l(()=>[a(R,{label:"userGuid"},{default:l(()=>[u("用户GUID")]),_:1}),a(R,{label:"nickname"},{default:l(()=>[u("用户昵称")]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(f)==="userGuid"?(_(),b(m,{key:0,label:"用户GUID",prop:"userGuid"},{default:l(()=>[a(p,{modelValue:o(i).userGuid,"onUpdate:modelValue":n[5]||(n[5]=e=>o(i).userGuid=e),placeholder:"请输入用户GUID",clearable:""},null,8,["modelValue"])]),_:1})):G("",!0),o(f)==="nickname"?(_(),b(m,{key:1,label:"用户昵称",prop:"nickname"},{default:l(()=>[a(p,{modelValue:o(i).nickname,"onUpdate:modelValue":n[6]||(n[6]=e=>o(i).nickname=e),placeholder:"请输入用户昵称",clearable:""},null,8,["modelValue"])]),_:1})):G("",!0),a(m,{label:"实际支付金额",prop:"actualPayAmount"},{default:l(()=>[a(ge,{modelValue:o(i).actualPayAmount,"onUpdate:modelValue":n[7]||(n[7]=e=>o(i).actualPayAmount=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),o(v)?(_(),b(m,{key:2,label:"智能体信息"},{default:l(()=>{var e;return[s("div",pa,[s("div",ga,[s("div",null,[fa,u(d(o(v).agentName),1)]),s("div",null,[va,u("¥"+d((o(v).price/100).toFixed(2)),1)]),s("div",null,[ha,u(d(o(v).isFeatured===1?"是":"否"),1)]),s("div",null,[ba,u(d(((e=o(v).creator)==null?void 0:e.nickname)||"未知"),1)])])])]}),_:1})):G("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const La=Xe(wa,[["__scopeId","data-v-de55df2b"]]);export{La as default};
