import{o as h,c as A,f as b,d as ie,r,a as F,b as e,w as t,h as n,A as T,D as re,a_ as de,e as ce,C as ue,u as pe,i as w,T as U,I as V,E as u,a$ as me,b0 as _e,b1 as fe,ax as ge,b2 as ve,p as we,l as he,q as be,s as xe,af as ye,v as Ee,y as Fe,x as Ce,Y as Le,O as ke,P as Ne,k as Te,Z as Ue}from"./index-2d10794a.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                  *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                     */const Ve={viewBox:"0 0 1024 1024",width:"1.2em",height:"1.2em"},Ae=b("path",{fill:"currentColor",d:"M544 864V672h128L512 480L352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6H544z"},null,-1),Se=[Ae];function qe(S,x){return h(),A("svg",Ve,Se)}const De={name:"ep-upload-filled",render:qe},Pe={class:"header-box"},Me=b("div",{class:"el-upload__text"},"将文件拖放到此处或单击上传。",-1),Be=b("div",{class:"el-upload__tip"}," 支持TXT、 MARKDOWN、 PDF、 HTML、 XLSX、 XLS、 DOCX、 CSV、 EML、 MSG、 PPTX、 PPT、 XML、 EPUB,一个文件上传 ",-1),Je=ie({__name:"KnowledgeDetailView",setup(S){const x=re(),q=pe(),D=x.query.guid,p=r(!1),P=r(!1),c=r(!1),m=r(!0),y=r(),M=()=>{c.value=!0};let C=r([]);const _=r([]),L=r(0),E=F({guid:D,pageSize:10,page:1}),f=async()=>{let l=await de(E);C.value=l.data.data,L.value=l.data.total,console.log(l,"文档列表接口响应")};f();const i=F({guid:"",fileName:"",fileUrl:""}),k=r(),B=F({fileName:[{required:!0,message:"请输入文档名",trigger:"change"}],fileUrl:[{required:!0,message:"请上传文档",trigger:"change"}]}),R=l=>{const o=l.name;let a="",d=[];d=o.split("."),Array.isArray(d)&&(a=d.pop().toLowerCase());const g=["txt","md","pdf","html","xlsx","xls","docx","csv","eml","msg","pptx","ppt","xml","epub"];if(l.size/1024/1024>15)return u.warning("文件大小必须小于15MB"),!1;if(!g.includes(a))return u.warning("不支持上传该文件类型"),!1},I=async l=>{const o=new FormData;o.append("file",l.file),p.value=!0;let a=await me(o);p.value=!1,i.fileName=l.file.name,i.fileUrl=a.data},X=()=>{i.fileName="",i.fileUrl="",m.value=!0},$=()=>{m.value=!1},K=()=>{p.value=!1,m.value=!0},z=l=>{y.value.clearFiles();const o=l[0];o.uid=_e(),y.value.handleStart(o)},G=async l=>{l.validate(async o=>{if(o)try{i.guid=x.query.guid,await fe(i),u.success("新增成功"),ge(()=>{var a;l.resetFields(),(a=y.value)==null||a.clearFiles(),_.value=[],c.value=!1}),f()}catch(a){throw u.error(a),new Error(a)}})},H=async l=>{await ve({fileGuid:l.guid}),u.success("删除成功"),f()},O=async l=>{E.page=l,f()},W=l=>{q.push({name:"knowledgefiledetail",query:{fileGuid:l.guid}})};return(l,o)=>{const a=we,d=he,g=be,v=xe,Y=ye,Z=Ee,j=Fe,J=Ce,Q=Le,ee=ce,le=De,te=ke,oe=Ne,ae=Te,ne=ue,N=Ue;return h(),A("div",null,[e(ee,{class:"wrapper"},{default:t(()=>[e(j,null,{default:t(()=>[b("div",Pe,[e(g,{inline:!0,class:"demo-form-inline"},{default:t(()=>[e(d,null,{default:t(()=>[e(a,{type:"primary",onClick:M},{default:t(()=>[w("新增知识库文档")]),_:1})]),_:1})]),_:1})]),U((h(),V(Z,{data:n(C),border:"",style:{width:"100%"}},{default:t(()=>[e(v,{prop:"sysId",label:"id",width:"80"}),e(v,{prop:"fileName",label:"文档名称"}),e(v,{prop:"modifyTime",label:"上传日期"}),e(v,{label:"操作",width:"300"},{default:t(s=>[e(a,{size:"small",type:"primary",onClick:se=>W(s.row)},{default:t(()=>[w("查看文档片段")]),_:2},1032,["onClick"]),e(Y,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该文档?",onConfirm:se=>H(s.row)},{reference:t(()=>[e(a,{size:"small",type:"danger"},{default:t(()=>[w("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[N,n(P)]])]),_:1}),e(Q,null,{default:t(()=>[e(J,{background:"",layout:"prev,pager, next",total:n(L),"current-page":n(E).page,onCurrentChange:O},null,8,["total","current-page"])]),_:1})]),_:1}),e(ne,{modelValue:n(c),"onUpdate:modelValue":o[3]||(o[3]=s=>T(c)?c.value=s:null),title:"上传文档",width:"600",draggable:""},{default:t(()=>[e(g,{ref_key:"addForm",ref:k,model:n(i),class:"demo-form-inline",rules:n(B),"label-width":"100px"},{default:t(()=>[U((h(),V(d,{label:"上传文档",prop:"fileUrl"},{default:t(()=>[e(oe,{ref:"upload",class:"upload-box","file-list":n(_),"onUpdate:fileList":o[0]||(o[0]=s=>T(_)?_.value=s:null),drag:"",limit:1,"http-request":s=>I(s),"on-remove":X,"on-success":$,"on-error":K,"on-exceed":z,"before-upload":R},{tip:t(()=>[Be]),default:t(()=>[e(te,{class:"el-icon--upload"},{default:t(()=>[e(le)]),_:1}),Me]),_:1},8,["file-list","http-request"])]),_:1})),[[N,n(p)]]),e(d,{label:"文档名称",prop:"fileName"},{default:t(()=>[e(ae,{modelValue:n(i).fileName,"onUpdate:modelValue":o[1]||(o[1]=s=>n(i).fileName=s),placeholder:"文档名称"},null,8,["modelValue"])]),_:1}),e(d,null,{default:t(()=>[e(a,{type:"primary",disabled:n(m),onClick:o[2]||(o[2]=s=>G(n(k)))},{default:t(()=>[w("提交")]),_:1},8,["disabled"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});export{Je as default};
