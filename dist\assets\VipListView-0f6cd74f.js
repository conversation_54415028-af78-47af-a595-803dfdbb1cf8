import{d as Fe,r as m,a as Y,by as Ye,c as h,b as e,w as a,h as l,A as E,cg as qe,E as q,e as Me,C as Le,o as _,T as Re,I as S,f as u,G as ne,H as re,aC as ze,U as s,i as n,F as G,ch as Oe,ci as Be,k as He,l as $e,m as je,n as Qe,X as Xe,p as Ze,q as Je,s as Ke,cj as We,ae as ea,v as aa,y as la,x as ta,Y as oa,ck as na,cl as ra,cf as sa,al as ua,am as ia,a3 as da,Z as ca,bb as ma,bc as pa,cm as _a,_ as fa}from"./index-2d10794a.js";/* empty css                   *//* empty css                        *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                  *//* empty css                     *//* empty css                       */const M=A=>(ma("data-v-53428334"),A=A(),pa(),A),ga={class:"header-box"},va={class:"mode-box"},ya=["onClick"],ba={class:"user-info"},ka={class:"user-details"},ha={class:"mobile"},wa={class:"package-info"},Va={class:"package-name"},Ta={class:"duration"},Ca={class:"amount-info"},Ga={class:"pay-amount"},Da={key:0,class:"inviter-info"},xa={class:"inviter-name"},Pa={key:1,class:"no-inviter"},Na={class:"commission-info"},Ea={key:0},Sa={key:0,class:"order-detail"},Aa={class:"dialog-footer"},Ia={key:0,class:"payment-result"},Ua={class:"dialog-footer"},Fa={class:"package-option"},Ya={class:"package-name"},qa={class:"package-price"},Ma={class:"package-preview"},La={class:"package-detail"},Ra=M(()=>u("strong",null,"套餐名称：",-1)),za=M(()=>u("strong",null,"套餐类型：",-1)),Oa=M(()=>u("strong",null,"套餐价格：",-1)),Ba=M(()=>u("strong",null,"套餐时长：",-1)),Ha={class:"dialog-footer"},$a=Fe({__name:"VipListView",setup(A){const L=m(!1),R=m(0),z=m(""),I=m(null),T=m(!1),d=m(null),D=m(!1),O=m(!1),U=m(),g=m(null),x=m(!1),B=m(!1),P=m(),k=m("userGuid"),F=m([]),w=m(null),N=Y({orderNo:""}),p=Y({merchantGuid:"",packageGuid:"",userGuid:"",nickname:"",actualPayAmount:0}),se={orderNo:[{required:!0,message:"请输入订单编号",trigger:"blur"}]},ue={packageGuid:[{required:!0,message:"请选择套餐",trigger:"change"}],userGuid:[{required:!0,message:"请输入用户GUID",trigger:"blur",validator:(o,r,y)=>{k.value==="userGuid"&&!r?y(new Error("请输入用户GUID")):y()}}],nickname:[{required:!0,message:"请输入用户昵称",trigger:"blur",validator:(o,r,y)=>{k.value==="nickname"&&!r?y(new Error("请输入用户昵称")):y()}}]},ie=Y([{name:"全部",value:""},{name:"待支付",value:100},{name:"已支付",value:200},{name:"取消支付",value:300},{name:"支付超时",value:400},{name:"已退款",value:500}]),c=Y({merchantGuid:"",orderStatus:"",packageType:"",orderNo:"",userMobile:"",startTime:"",endTime:"",page:1,limit:20}),H=m([]),Z=o=>{switch(o){case 100:return"warning";case 200:return"success";case 300:return"info";case 400:return"danger";case 500:return"primary";default:return"info"}},de=o=>{z.value=o.toString(),c.orderStatus=o,c.page=1,V()},ce=o=>{o?(c.startTime=o[0],c.endTime=o[1]):(c.startTime="",c.endTime="")},V=async()=>{try{L.value=!0;const o=await qe(c);o.data&&(R.value=o.data.total||0,H.value=o.data.data||[])}catch(o){console.error("获取订单列表失败:",o),q.error("获取订单列表失败"),H.value=[],R.value=0}finally{L.value=!1}},me=()=>{c.page=1,V()},pe=()=>{Object.assign(c,{orderStatus:"",packageType:"",orderNo:"",userMobile:"",startTime:"",endTime:"",page:1,limit:20}),z.value="",I.value=null,V()},_e=o=>{c.page=o,V()},fe=o=>{c.limit=o,c.page=1,V()},ge=o=>{d.value=o,T.value=!0},ve=()=>{T.value=!1,d.value=null},ye=o=>{switch(o){case 1:return"月卡";case 2:return"季卡";case 3:return"年卡";case 4:return"自定义";default:return"未知"}},be=async()=>{try{const o=await _a({});o.data&&(F.value=o.data.data||o.data||[])}catch(o){console.error("获取套餐列表失败:",o),q.error("获取套餐列表失败"),F.value=[]}},ke=()=>{x.value=!0,be(),J()},J=()=>{var o;Object.assign(p,{merchantGuid:"",packageGuid:"",userGuid:"",nickname:"",actualPayAmount:0}),k.value="userGuid",w.value=null,(o=P.value)==null||o.clearValidate()},he=o=>{w.value=F.value.find(r=>r.guid===o)||null},we=o=>{var r;o==="userGuid"?p.nickname="":p.userGuid="",(r=P.value)==null||r.clearValidate()},$=()=>{x.value=!1,J()},Ve=()=>{D.value=!0,K()},K=()=>{var o;N.orderNo="",g.value=null,(o=U.value)==null||o.clearValidate()},W=()=>{D.value=!1,K()},Te=async()=>{if(U.value)try{await U.value.validate(),O.value=!0;const o=await Oe({orderNo:N.orderNo});g.value=o.data}catch(o){console.error("查询付款状态失败:",o),q.error("查询付款状态失败"),g.value=null}finally{O.value=!1}},Ce=async()=>{if(P.value)try{await P.value.validate(),B.value=!0;const o={packageGuid:p.packageGuid,actualPayAmount:p.actualPayAmount};k.value==="userGuid"?o.userGuid=p.userGuid:o.nickname=p.nickname;const{data:r}=await Be(o);r.data?($(),V()):q.warning(r.message)}catch(o){console.error("添加会员失败:",o)}finally{B.value=!1}};return Ye(()=>{V()}),(o,r)=>{const y=He,f=$e,C=je,ee=Qe,Ge=Xe,b=Ze,j=Je,v=Ke,ae=We,Q=ea,De=aa,xe=la,Pe=ta,Ne=oa,Ee=Me,i=na,le=ra,X=Le,Se=sa,te=ua,Ae=ia,Ie=da,Ue=ca;return _(),h("div",null,[e(Ee,{class:"wrapper"},{default:a(()=>[Re((_(),S(xe,null,{default:a(()=>[u("div",ga,[e(j,{inline:!0,model:l(c),class:"demo-form-inline"},{default:a(()=>[e(f,{label:"订单编号"},{default:a(()=>[e(y,{modelValue:l(c).orderNo,"onUpdate:modelValue":r[0]||(r[0]=t=>l(c).orderNo=t),placeholder:"请输入订单编号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"用户手机号"},{default:a(()=>[e(y,{modelValue:l(c).userMobile,"onUpdate:modelValue":r[1]||(r[1]=t=>l(c).userMobile=t),placeholder:"请输入用户手机号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"订单状态"},{default:a(()=>[u("div",va,[(_(!0),h(ne,null,re(l(ie),(t,oe)=>(_(),h("div",{class:ze(["item",{active:l(z)===t.value}]),key:oe,onClick:ja=>de(t.value)},s(t.name),11,ya))),128))])]),_:1}),e(f,{label:"套餐类型"},{default:a(()=>[e(ee,{modelValue:l(c).packageType,"onUpdate:modelValue":r[2]||(r[2]=t=>l(c).packageType=t),placeholder:"请选择套餐类型",clearable:""},{default:a(()=>[e(C,{label:"全部",value:""}),e(C,{label:"月卡",value:1}),e(C,{label:"季卡",value:2}),e(C,{label:"年卡",value:3}),e(C,{label:"自定义",value:4})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"时间范围"},{default:a(()=>[e(Ge,{modelValue:l(I),"onUpdate:modelValue":r[3]||(r[3]=t=>E(I)?I.value=t:null),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:ce},null,8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(b,{type:"primary",onClick:me},{default:a(()=>[n("搜索")]),_:1}),e(b,{onClick:pe},{default:a(()=>[n("重置")]),_:1}),e(b,{type:"primary",onClick:Ve},{default:a(()=>[n("查询付款")]),_:1}),e(b,{type:"success",onClick:ke},{default:a(()=>[n("新增会员")]),_:1})]),_:1})]),_:1},8,["model"])]),e(De,{data:l(H),border:"",style:{width:"100%"}},{default:a(()=>[e(v,{prop:"sysId",label:"订单ID",width:"80"}),e(v,{label:"购买用户",width:"200"},{default:a(t=>[u("div",ba,[e(ae,{src:t.row.buyer.headImgurl,size:30},null,8,["src"]),u("div",ka,[u("div",null,s(t.row.buyer.nickname),1),u("div",ha,s(t.row.buyer.mobile),1)])])]),_:1}),e(v,{prop:"orderNo",label:"订单编号",width:"200"}),e(v,{label:"套餐信息",width:"200"},{default:a(t=>[u("div",wa,[u("div",Va,s(t.row.package.packageName),1),u("div",Ta,s(t.row.durationDays)+"天",1)])]),_:1}),e(v,{label:"金额信息",width:"150"},{default:a(t=>[u("div",Ca,[u("div",Ga,"支付: ¥"+s(t.row.payAmountYuan),1)])]),_:1}),e(v,{prop:"orderStatusText",label:"订单状态",width:"100"},{default:a(t=>[e(Q,{type:Z(t.row.orderStatus),size:"small"},{default:a(()=>[n(s(t.row.orderStatusText),1)]),_:2},1032,["type"])]),_:1}),e(v,{label:"邀请人",width:"150"},{default:a(t=>[t.row.inviter?(_(),h("div",Da,[e(ae,{src:t.row.inviter.headImgurl,size:24},null,8,["src"]),u("span",xa,s(t.row.inviter.nickname),1)])):(_(),h("span",Pa,"无"))]),_:1}),e(v,{label:"佣金信息",width:"120"},{default:a(t=>[u("div",Na,[u("div",null,"平台费: ¥"+s(t.row.platformFeeYuan),1),t.row.inviterCommissionYuan?(_(),h("div",Ea," 邀请佣金: ¥"+s(t.row.inviterCommissionYuan),1)):G("",!0)])]),_:1}),e(v,{prop:"createTimeText",label:"创建时间",width:"160"}),e(v,{prop:"payTimeText",label:"支付时间",width:"160"}),e(v,{prop:"expireTimeText",label:"到期时间",width:"160"}),e(v,{label:"操作",width:"120",fixed:"right"},{default:a(t=>[e(b,{size:"small",type:"primary",onClick:oe=>ge(t.row)},{default:a(()=>[n(" 查看详情 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[Ue,l(L)]]),e(Ne,null,{default:a(()=>[e(Pe,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:l(R),"current-page":l(c).page,"page-size":l(c).limit,"page-sizes":[10,20,50,100],onCurrentChange:_e,onSizeChange:fe},null,8,["total","current-page","page-size"])]),_:1})]),_:1}),e(X,{modelValue:l(T),"onUpdate:modelValue":r[5]||(r[5]=t=>E(T)?T.value=t:null),title:"订单详情",width:"800px","before-close":ve},{footer:a(()=>[u("span",Aa,[e(b,{onClick:r[4]||(r[4]=t=>T.value=!1)},{default:a(()=>[n("关闭")]),_:1})])]),default:a(()=>[l(d)?(_(),h("div",Sa,[e(le,{column:2,border:""},{default:a(()=>[e(i,{label:"订单ID"},{default:a(()=>[n(s(l(d).sysId),1)]),_:1}),e(i,{label:"订单编号"},{default:a(()=>[n(s(l(d).orderNo),1)]),_:1}),e(i,{label:"交易流水号"},{default:a(()=>[n(s(l(d).transactionNo||"无"),1)]),_:1}),e(i,{label:"订单状态"},{default:a(()=>[e(Q,{type:Z(l(d).orderStatus)},{default:a(()=>[n(s(l(d).orderStatusText),1)]),_:1},8,["type"])]),_:1}),e(i,{label:"购买用户"},{default:a(()=>[n(s(l(d).buyerNickname),1)]),_:1}),e(i,{label:"用户手机"},{default:a(()=>[n(s(l(d).buyerMobile),1)]),_:1}),e(i,{label:"套餐名称"},{default:a(()=>[n(s(l(d).packageName),1)]),_:1}),e(i,{label:"套餐类型"},{default:a(()=>[n(s(l(d).packageTypeText),1)]),_:1}),e(i,{label:"套餐时长"},{default:a(()=>[n(s(l(d).durationDays)+"天",1)]),_:1}),e(i,{label:"原价"},{default:a(()=>[n("¥"+s(l(d).originalAmountYuan),1)]),_:1}),e(i,{label:"支付金额"},{default:a(()=>[n("¥"+s(l(d).payAmountYuan),1)]),_:1}),e(i,{label:"平台费用"},{default:a(()=>[n("¥"+s(l(d).platformFeeYuan),1)]),_:1}),e(i,{label:"邀请人"},{default:a(()=>[n(s(l(d).inviterNickname||"无"),1)]),_:1}),e(i,{label:"邀请佣金"},{default:a(()=>[n("¥"+s(l(d).inviterCommissionYuan||"0"),1)]),_:1}),e(i,{label:"创建时间"},{default:a(()=>[n(s(l(d).createTimeText),1)]),_:1}),e(i,{label:"支付时间"},{default:a(()=>[n(s(l(d).payTimeText||"未支付"),1)]),_:1}),e(i,{label:"到期时间"},{default:a(()=>[n(s(l(d).expireTimeText),1)]),_:1})]),_:1})])):G("",!0)]),_:1},8,["modelValue"]),e(X,{modelValue:l(D),"onUpdate:modelValue":r[7]||(r[7]=t=>E(D)?D.value=t:null),title:"查询付款状态",width:"700px","before-close":W},{footer:a(()=>[u("span",Ua,[e(b,{onClick:W},{default:a(()=>[n("关闭")]),_:1}),e(b,{type:"primary",onClick:Te,loading:l(O)},{default:a(()=>[n("查询")]),_:1},8,["loading"])])]),default:a(()=>[e(j,{model:l(N),rules:se,ref_key:"queryPaymentFormRef",ref:U,"label-width":"80px"},{default:a(()=>[e(f,{label:"订单编号",prop:"orderNo",required:""},{default:a(()=>[e(y,{modelValue:l(N).orderNo,"onUpdate:modelValue":r[6]||(r[6]=t=>l(N).orderNo=t),placeholder:"请输入订单编号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),l(g)?(_(),h("div",Ia,[e(Se,{"content-position":"left"},{default:a(()=>[n("查询结果")]),_:1}),e(le,{column:2,border:""},{default:a(()=>[e(i,{label:"订单编号"},{default:a(()=>[n(s(l(g).orderNo),1)]),_:1}),e(i,{label:"订单GUID"},{default:a(()=>[n(s(l(g).orderGuid),1)]),_:1}),e(i,{label:"支付状态"},{default:a(()=>[e(Q,{type:l(g).isPaid?"success":"warning"},{default:a(()=>[n(s(l(g).orderStatusText),1)]),_:1},8,["type"])]),_:1}),e(i,{label:"订单状态码"},{default:a(()=>[n(s(l(g).orderStatus),1)]),_:1}),e(i,{label:"套餐名称"},{default:a(()=>[n(s(l(g).packageName),1)]),_:1}),e(i,{label:"支付金额"},{default:a(()=>[n("¥"+s(l(g).payAmount),1)]),_:1}),e(i,{label:"支付时间",span:2},{default:a(()=>[n(s(l(g).payTimeText||"未支付"),1)]),_:1})]),_:1})])):G("",!0)]),_:1},8,["modelValue"]),e(X,{modelValue:l(x),"onUpdate:modelValue":r[13]||(r[13]=t=>E(x)?x.value=t:null),title:"新增会员",width:"600px","before-close":$},{footer:a(()=>[u("span",Ha,[e(b,{onClick:$},{default:a(()=>[n("取消")]),_:1}),e(b,{type:"primary",onClick:Ce,loading:l(B)},{default:a(()=>[n("确认添加")]),_:1},8,["loading"])])]),default:a(()=>[e(j,{model:l(p),rules:ue,ref_key:"addFormRef",ref:P,"label-width":"120px"},{default:a(()=>[e(f,{label:"套餐选择",prop:"packageGuid",required:""},{default:a(()=>[e(ee,{modelValue:l(p).packageGuid,"onUpdate:modelValue":r[8]||(r[8]=t=>l(p).packageGuid=t),placeholder:"请选择套餐",style:{width:"100%"},onChange:he},{default:a(()=>[(_(!0),h(ne,null,re(l(F),t=>(_(),S(C,{key:t.guid,label:`${t.packageName} - ¥${t.salePrice/100}`,value:t.guid},{default:a(()=>[u("div",Fa,[u("span",Ya,s(t.packageName),1),u("span",qa,"¥"+s(t.salePrice/100),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"用户选择",required:""},{default:a(()=>[e(Ae,{modelValue:l(k),"onUpdate:modelValue":r[9]||(r[9]=t=>E(k)?k.value=t:null),onChange:we},{default:a(()=>[e(te,{label:"userGuid"},{default:a(()=>[n("用户GUID")]),_:1}),e(te,{label:"nickname"},{default:a(()=>[n("用户昵称")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(k)==="userGuid"?(_(),S(f,{key:0,label:"用户GUID",prop:"userGuid"},{default:a(()=>[e(y,{modelValue:l(p).userGuid,"onUpdate:modelValue":r[10]||(r[10]=t=>l(p).userGuid=t),placeholder:"请输入用户GUID",clearable:""},null,8,["modelValue"])]),_:1})):G("",!0),l(k)==="nickname"?(_(),S(f,{key:1,label:"用户昵称",prop:"nickname"},{default:a(()=>[e(y,{modelValue:l(p).nickname,"onUpdate:modelValue":r[11]||(r[11]=t=>l(p).nickname=t),placeholder:"请输入用户昵称",clearable:""},null,8,["modelValue"])]),_:1})):G("",!0),e(f,{label:"实际支付金额",prop:"nickname"},{default:a(()=>[e(Ie,{modelValue:l(p).actualPayAmount,"onUpdate:modelValue":r[12]||(r[12]=t=>l(p).actualPayAmount=t),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(w)?(_(),S(f,{key:2,label:"套餐信息"},{default:a(()=>[u("div",Ma,[u("div",La,[u("div",null,[Ra,n(s(l(w).packageName),1)]),u("div",null,[za,n(s(ye(l(w).packageType)),1)]),u("div",null,[Oa,n("¥"+s(l(w).salePrice/100),1)]),u("div",null,[Ba,n(s(l(w).durationDays)+"天",1)])])])]),_:1})):G("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const dl=fa($a,[["__scopeId","data-v-53428334"]]);export{dl as default};
