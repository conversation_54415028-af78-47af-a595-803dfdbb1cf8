<script setup lang="ts">
import {
  getPayAgentWithdrawListApi,
  getAiAgentCategoryApi,
  createAiAgentCategoryApi,
  updateAiAgentCategoryApi,
  deleteAiAgentCategoryApi
} from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';


// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});

// 数据类型定义
interface CategoryItem {
  guid: string;
  status: number;
  sysId: number;
  merchantGuid: string;
  categoryName: string;
  categoryDesc: string;
  categoryIcon: string;
  sortOrder: number;
  createTime: string;
}

// 列表数据
let categoryList = ref<CategoryItem[]>([]);
// let tenantList: any = ref([]);

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref<FormInstance>();
const actionType = ref('add'); // 'add' 或 'edit'

// 表单数据
const formData = reactive({
  guid: '',
  merchantGuid: '',
  categoryName: '',
  sortOrder: 1,
  status: 1
});

// 表单验证规则
const rules: FormRules = {
  merchantGuid: [{ required: true, message: '请选择商户', trigger: 'change' }],
  categoryName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
};

const getPayAgentWithdrawList = async () => {
  loading.value = true;
  try {
    let res = await getPayAgentWithdrawListApi(searchParams);
    console.log(res, '-------------------dsadas')
  } catch (error) {
  } finally {
    loading.value = false;
  }
};
getPayAgentWithdrawList()
// 获取分类列表
const getList = async () => {
  loading.value = true;
  try {
    let res = await getAiAgentCategoryApi(searchParams);
    if (res.data && Array.isArray(res.data)) {
      // 直接数组格式
      categoryList.value = res.data;
      total.value = res.data.length;
    } else if (res.data && res.data.data) {
      // 分页格式
      categoryList.value = res.data.data;
      total.value = res.data.total;
    }
  } catch (error) {
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索功能
const handleSearch = () => {
  searchParams.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  searchParams.page = 1;
  getList();
};

// 分页切换
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getList();
};

// 每页条数切换
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  getList();
};


getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 表格区域 -->
        <el-table :data="categoryList" border style="width: 100%" v-loading="loading">
          <el-table-column prop="sysId" label="ID" />
          <el-table-column prop="categoryName" label="分类名称" />
          <el-table-column prop="categoryDesc" label="分类描述" show-overflow-tooltip />
          <el-table-column prop="categoryIcon" label="分类图标" width="100">
            <template #default="scope">
              <el-image v-if="scope.row.categoryIcon" :src="scope.row.categoryIcon" style="width: 40px; height: 40px"
                fit="cover" />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sortOrder" label="排序" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :prev-text="'上一页'" :next-text="'下一页'" @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>

  </div>
</template>

<style scoped lang="scss">
.search-box {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
}
</style>
