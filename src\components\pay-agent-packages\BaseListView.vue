<script setup lang="ts">
import {
  getPayAgentPackageListApi,
  createPayAgentPackageApi,
  updatePayAgentPackageApi,
  deletePayAgentPackageApi
} from '@/api';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';

// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '', // 商户uuid，不需要填写
  pageSize: 100,
  page: 1,
});

// 数据类型定义
interface PackageItem {
  guid: string;
  status: number;
  sysId: number;
  merchantGuid: string;
  packageName: string;
  packageDesc: string;
  packageType: number;
  durationDays: number;
  originalPrice: number;
  salePrice: number;
  packageIcon: string;
  packageFeatures: {
    [key: string]: string;
  };
  rewardPoints: number;
  sortOrder: number;
  isHot: number;
  isRecommended: number;
  createTime: number;
  updateTime: number;
  deletedAt: string | null;
  modifyTime: string;
  packageTypeText: string;
  statusText: string;
  originalPriceYuan: string;
  salePriceYuan: string;
  rewardPointsText: string;
  createTimeText: string;
}

// 列表数据
const packageList = ref<PackageItem[]>([]);

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref<FormInstance>();
const actionType = ref('add'); // 'add' 或 'edit'

// 表单数据
const formData = reactive({
  packageGuid: '',
  merchantGuid: '',
  packageName: '',
  packageType: 1,
  durationDays: 30,
  salePrice: 0,
  originalPrice: 0,
  packageIcon: '',
  rewardPoints: 0,
  sortOrder: 1,
  status: 1,
  packageDesc: ''
});

// 表单验证规则
const rules: FormRules = {
  packageName: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
  packageType: [{ required: true, message: '请选择套餐类型', trigger: 'change' }],
  durationDays: [{ required: true, message: '请输入有效天数', trigger: 'blur' }],
  salePrice: [{ required: true, message: '请输入售价', trigger: 'blur' }],
  originalPrice: [{ required: true, message: '请输入原价', trigger: 'blur' }],
  rewardPoints: [{ required: true, message: '请输入奖励积分', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  packageDesc: [{ required: true, message: '请输入套餐说明', trigger: 'blur' }]
};

// 套餐类型选项
const packageTypeOptions = [
  { label: '月卡', value: 1 },
  { label: '季卡', value: 2 },
  { label: '年卡', value: 3 },
  { label: '自定义', value: 4 }
];

// 获取套餐列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getPayAgentPackageListApi(searchParams);
    if (res.data && Array.isArray(res.data)) {
      // 直接数组格式
      packageList.value = res.data;
      total.value = res.data.length;
    } else if (res.data && res.data.data) {
      // 分页格式
      packageList.value = res.data.data;
      total.value = res.data.total;
    }
  } catch (error) {
    ElMessage.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索功能
const handleSearch = () => {
  searchParams.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  searchParams.page = 1;
  getList();
};

// 分页切换
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getList();
};

// 每页条数切换
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  getList();
};

// 获取套餐类型标签样式
const getPackageTypeTag = (type: number) => {
  const tagMap = {
    1: 'success', // 月卡
    2: 'warning', // 季卡
    3: 'info',    // 年卡
  };
  return tagMap[type] || '';
};

// 获取状态标签样式
const getStatusTag = (status: number) => {
  return status === 1 ? 'success' : 'danger';
};

// 新增套餐
const onAdd = () => {
  actionType.value = 'add';
  dialogTitle.value = '新增套餐';

  // 重置表单数据
  Object.assign(formData, {
    packageGuid: '',
    merchantGuid: '',
    packageName: '',
    packageType: 1,
    durationDays: 30,
    salePrice: 0,
    originalPrice: 0,
    packageIcon: '',
    rewardPoints: 0,
    sortOrder: 1,
    status: 1,
    packageDesc: ''
  });

  dialogVisible.value = true;
};

// 编辑套餐
const onEdit = (item: PackageItem) => {
  actionType.value = 'edit';
  dialogTitle.value = '编辑套餐';

  // 填充表单数据
  Object.assign(formData, {
    packageGuid: item.guid,
    merchantGuid: item.merchantGuid,
    packageName: item.packageName,
    packageType: item.packageType,
    durationDays: item.durationDays,
    salePrice: item.salePrice,
    originalPrice: item.originalPrice,
    packageIcon: item.packageIcon,
    rewardPoints: item.rewardPoints,
    sortOrder: item.sortOrder,
    status: item.status,
    packageDesc: item.packageDesc
  });

  dialogVisible.value = true;
};

// 删除套餐
const onDelete = async (item: PackageItem) => {
  try {
    await deletePayAgentPackageApi({
      packageGuid: item.guid,
      merchantGuid: '' // 不用填
    });
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = {
          ...formData,
          salePrice: formData.salePrice,
          originalPrice: formData.originalPrice,
          merchantGuid: formData.merchantGuid || 'e108201b02ae42e686bcc4c302cbbd11' // 使用默认商户GUID
        };

        if (actionType.value === 'edit') {
          // 更新套餐
          await updatePayAgentPackageApi(submitData);
          ElMessage.success('更新成功');
        } else {
          // 创建套餐
          const { packageGuid, ...createData } = submitData;
          await createPayAgentPackageApi(createData);
          ElMessage.success('创建成功');
        }

        dialogVisible.value = false;
        getList();
      } catch (error) {
        ElMessage.error(actionType.value === 'edit' ? '更新失败' : '创建失败');
      }
    }
  });
};

// 初始化加载数据
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 搜索筛选区域 -->
        <div class="search-box">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="success" @click="onAdd">新增套餐</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table :data="packageList" border style="width: 100%" v-loading="loading">
          <el-table-column prop="sysId" label="ID" width="80" />
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="packageDesc" label="套餐描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="packageTypeText" label="套餐类型" width="100">
            <template #default="scope">
              <el-tag :type="getPackageTypeTag(scope.row.packageType)">
                {{ scope.row.packageTypeText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="durationDays" label="有效期(天)" width="100" />
          <el-table-column prop="originalPriceYuan" label="原价(元)" width="100" />
          <el-table-column prop="salePriceYuan" label="售价(元)" width="100" />
          <el-table-column prop="rewardPointsText" label="奖励积分" width="100" />
          <!-- <el-table-column prop="sortOrder" label="排序" width="80" /> -->
          <el-table-column prop="statusText" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="getStatusTag(scope.row.status)">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTimeText" label="创建时间" width="180" />
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm title="确认删除该套餐吗？" confirm-button-text="确定" cancel-button-text="取消"
                @confirm="onDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :prev-text="'上一页'" :next-text="'下一页'" @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>

    <!-- 套餐表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="套餐名称" prop="packageName">
          <el-input v-model="formData.packageName" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="套餐类型" prop="packageType">
          <el-select v-model="formData.packageType" placeholder="请选择套餐类型" style="width: 100%">
            <el-option v-for="option in packageTypeOptions" :key="option.value" :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="有效天数" prop="durationDays">
          <el-input-number v-model="formData.durationDays" :min="1" :max="9999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="售价(元)" prop="salePrice">
          <el-input-number v-model="formData.salePrice" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="原价(元)" prop="originalPrice">
          <el-input-number v-model="formData.originalPrice" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="奖励积分" prop="rewardPoints">
          <el-input-number v-model="formData.rewardPoints" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="formData.sortOrder" :min="0" :max="999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="套餐说明" prop="packageDesc">
          <el-input v-model="formData.packageDesc" type="textarea" :rows="3" maxlength="30" show-word-limit
            placeholder="请输入套餐说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-box {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
}
</style>