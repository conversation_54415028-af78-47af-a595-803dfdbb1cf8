import{d as W,r as c,a as w,z as X,c as p,b as e,w as a,h as r,A as Y,B as Z,e as ee,C as le,D as ae,o as i,f,i as v,F as m,G as ne,H as te,I as x,E as b,J as oe,K as re,L as se,M as ie,p as ue,s as de,N as pe,v as _e,y as ce,m as me,n as be,l as ge,O as fe,P as ye,k as ke,q as ve,_ as we}from"./index-5837f9dc.js";/* empty css                  *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                  *//* empty css                    */import{_ as he,a as Ie}from"./plus-acd24b23.js";/* empty css               *//* empty css                  *//* empty css                        *//* empty css                        */const Te={class:"header-box"},Ve={key:0},Ee={key:1},Be={key:2},Ce={key:3},xe=["src"],De=["src"],Fe={class:"el-upload-list__item-actions"},Ue=["onClick"],Ae=W({__name:"BannerView",setup(qe){const h=ae();let I=c([]);const T=w({merchantGuid:""});let u=c(!1),g=c("");const t=w({merchantGuid:"",bannerType:"user_pay_agent",bannerImg:"",linkType:0,linkUrl:"",bannerDesc:"",guid:""});let D=c([{label:"不跳转",value:0},{label:"跳转h5网页",value:1},{label:"跳转小程序内部页",value:2},{label:"跳转其他小程序",value:3}]);const V=c(),F=w({bannerImg:[{required:!0,message:"请上传图片",trigger:"blur"}],linkType:[{required:!0,message:"请选择跳转类型",trigger:"blur"}]}),E=c(),U=n=>{let o=["image/jpg","image/png","image/jpeg"];if(o.includes(n.type)){if(n.size/1024/1024>2)return b.error("图片必须小于2M"),!1}else return b.warning("当前图片仅支持格式为："+o.join(" ，")),!1;return!0},A=async n=>{const o=new FormData;o.append("img",n.file);let s=await oe(o);t.bannerImg=s.data},y=async()=>{T.merchantGuid=h.query.guid;let n=await Z(T);I.value=n.data},q=n=>{t.bannerImg="",E.value.clearFiles()},z=async n=>{await re({guid:n.guid}),b.success("删除成功"),y()},G=async n=>{t.merchantGuid=h.query.guid,n.validate(async o=>{if(o)try{g.value==="add"?(await se(t),b.success("新增成功")):g.value==="edit"&&(await ie(t),b.success("修改成功")),n.resetFields(),u.value=!1,y()}catch(s){throw b.error(s),new Error(s)}})},L=n=>{console.log(n,"itemitem"),u.value=!0,t.guid=n.guid,t.bannerType=n.bannerType,t.bannerImg=n.bannerImg,t.linkType=n.linkType,t.bannerDesc=n.bannerDesc,g.value="edit"},N=()=>{u.value=!0,g.value="add"};return X(()=>{y()}),y(),(n,o)=>{const s=ue,d=de,R=pe,M=_e,j=ce,$=ee,O=me,P=be,_=ge,S=he,B=fe,H=Ie,J=ye,C=ke,K=ve,Q=le;return i(),p("div",null,[e($,{class:"wrapper"},{default:a(()=>[e(j,null,{default:a(()=>[f("div",Te,[e(s,{type:"primary",onClick:N},{default:a(()=>[v("上传图片")]),_:1})]),e(M,{data:r(I),border:"",style:{width:"100%"}},{default:a(()=>[e(d,{prop:"sysId",label:"Id",width:"80"}),e(d,{prop:"typeText",label:"banner类型",width:"220"}),e(d,{prop:"bannerImg",label:"banner图片"},{default:a(l=>[e(R,{src:l.row.bannerImg,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e(d,{prop:"linkType",label:"跳转类型"},{default:a(l=>[l.row.linkType===0?(i(),p("span",Ve,"不跳转")):m("",!0),l.row.linkType===1?(i(),p("span",Ee,"跳转h5网页")):m("",!0),l.row.linkType===2?(i(),p("span",Be,"跳转小程序内部页面")):m("",!0),l.row.linkType===3?(i(),p("span",Ce,"跳转其他小程序")):m("",!0)]),_:1}),e(d,{prop:"linkUrl",label:"跳转地址"}),e(d,{prop:"bannerDesc",label:"banner描述"}),e(d,{label:"操作"},{default:a(l=>[e(s,{size:"small",type:"primary",onClick:k=>L(l.row)},{default:a(()=>[v("编辑")]),_:2},1032,["onClick"]),e(s,{size:"small",type:"danger",onClick:k=>z(l.row)},{default:a(()=>[v("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(Q,{modelValue:r(u),"onUpdate:modelValue":o[4]||(o[4]=l=>Y(u)?u.value=l:u=l),title:"修改/新增 banner",width:"600px"},{default:a(()=>[e(K,{ref_key:"addForm",ref:V,model:r(t),class:"demo-form-inline","label-width":"100px",rules:r(F)},{default:a(()=>[e(_,{label:"跳转类型",prop:"linkType"},{default:a(()=>[e(P,{modelValue:r(t).linkType,"onUpdate:modelValue":o[0]||(o[0]=l=>r(t).linkType=l),placeholder:"请选择"},{default:a(()=>[(i(!0),p(ne,null,te(r(D),(l,k)=>(i(),x(O,{label:l.label,value:l.value,key:k},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(g)==="edit"?(i(),x(_,{key:0,label:"当前图片"},{default:a(()=>[r(t).bannerImg?(i(),p("img",{key:0,src:r(t).bannerImg,class:"edit-img"},null,8,xe)):m("",!0)]),_:1})):m("",!0),e(_,{label:"图片",prop:"bannerImg"},{default:a(()=>[e(J,{ref_key:"uploadImgRef",ref:E,class:"avatar-uploader","list-type":"picture-card","before-upload":U,"http-request":A,limit:1},{file:a(({file:l})=>[f("div",null,[f("img",{class:"el-upload-list__item-thumbnail",src:l.url,alt:""},null,8,De),f("span",Fe,[f("span",{class:"el-upload-list__item-delete",onClick:k=>q(l)},[e(B,null,{default:a(()=>[e(H)]),_:1})],8,Ue)])])]),default:a(()=>[e(B,{size:"30"},{default:a(()=>[e(S)]),_:1})]),_:1},512)]),_:1}),e(_,{label:"跳转地址"},{default:a(()=>[e(C,{modelValue:r(t).linkUrl,"onUpdate:modelValue":o[1]||(o[1]=l=>r(t).linkUrl=l),placeholder:"跳转地址"},null,8,["modelValue"])]),_:1}),e(_,{label:"banner描述"},{default:a(()=>[e(C,{modelValue:r(t).bannerDesc,"onUpdate:modelValue":o[2]||(o[2]=l=>r(t).bannerDesc=l),placeholder:"banner描述"},null,8,["modelValue"])]),_:1}),e(_,null,{default:a(()=>[e(s,{type:"primary",onClick:o[3]||(o[3]=l=>G(r(V)))},{default:a(()=>[v("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Je=we(Ae,[["__scopeId","data-v-92a795d2"]]);export{Je as default};
