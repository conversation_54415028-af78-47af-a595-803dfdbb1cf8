import{d as R,a5 as M,r as c,a as f,a6 as P,c as z,b as e,w as a,h as l,A as Y,ar as Z,e as $,C as j,o as C,T as H,I as J,f as K,i as b,as as O,E,k as Q,l as W,p as X,q as ee,s as ae,N as le,v as te,y as oe,x as ne,Y as re,Z as se,_ as ie}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                        *//* empty css                     */const de={class:"hearder-box"},ue=R({__name:"DigitListView",setup(me){const x=M(),h=c(0),s=c(!1);let i=c(!1);const v=c(),n=f({merchantGuid:"",parentUid:"",mobile:"",pageSize:10,page:1}),p=f({merchantGuid:"",mobile:""});let w=P(()=>x.getTenantInfo.guid);const I=f({mobile:[{required:!0,message:"请输入正确手机号码",trigger:"blur"},{pattern:/^1\d{10}$/,message:"手机号必须是11位数字",trigger:"blur"}]});let y=c([]);const _=async()=>{s.value=!0,n.merchantGuid=w.value;let r=await Z(n);s.value=!1,h.value=r.data.total,y.value=r.data.data},k=async r=>{n.page=r,_()},L=()=>{_()},U=()=>{i.value=!0,p.merchantGuid=w.value},F=async r=>{r.validate(async t=>{if(t){s.value=!0;try{await O(p),E.success("新增成功"),i.value=!1,s.value=!1,r.resetFields(),_()}catch(d){throw E.error(d),s.value=!1,new Error(d)}}})};return _(),(r,t)=>{const d=Q,u=W,g=X,V=ee,m=ae,A=le,N=te,T=oe,B=ne,D=re,G=$,S=j,q=se;return C(),z("div",null,[e(G,{class:"wrapper"},{default:a(()=>[H((C(),J(T,null,{default:a(()=>[K("div",de,[e(V,{inline:!0,model:l(n),class:"demo-form-inline"},{default:a(()=>[e(u,{label:"手机号码"},{default:a(()=>[e(d,{modelValue:l(n).mobile,"onUpdate:modelValue":t[0]||(t[0]=o=>l(n).mobile=o),placeholder:"手机号码"},null,8,["modelValue"])]),_:1}),e(u,{label:"上级用户Id"},{default:a(()=>[e(d,{modelValue:l(n).parentUid,"onUpdate:modelValue":t[1]||(t[1]=o=>l(n).parentUid=o),placeholder:"上级用户Id"},null,8,["modelValue"])]),_:1}),e(u,null,{default:a(()=>[e(g,{type:"primary",onClick:L},{default:a(()=>[b("搜索")]),_:1})]),_:1}),e(u,null,{default:a(()=>[e(g,{type:"primary",onClick:U},{default:a(()=>[b("新增")]),_:1})]),_:1})]),_:1},8,["model"])]),e(N,{data:l(y),border:"",style:{width:"100%"}},{default:a(()=>[e(m,{prop:"platformUserSysId",label:"用户Id",width:"80"}),e(m,{prop:"channelNickname",label:"数字人昵称",width:"180"}),e(m,{prop:"userNickname",label:"用户昵称",width:"180"}),e(m,{prop:"channelLogoImg",label:"数字人头像",width:"120"},{default:a(o=>[e(A,{src:o.row.channelLogoImg,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src"])]),_:1}),e(m,{prop:"mobile",label:"手机号码",width:"120"}),e(m,{prop:"modifyTime",label:"支付时间"})]),_:1},8,["data"])]),_:1})),[[q,l(s)]]),e(D,null,{default:a(()=>[e(B,{background:"",layout:"prev,pager, next",total:l(h),"current-page":l(n).page,onCurrentChange:k},null,8,["total","current-page"])]),_:1})]),_:1}),e(S,{modelValue:l(i),"onUpdate:modelValue":t[4]||(t[4]=o=>Y(i)?i.value=o:i=o),title:"新增数字人",width:"500px"},{default:a(()=>[e(V,{ref_key:"addForm",ref:v,model:l(p),class:"demo-form-inline","label-width":"100px",rules:l(I)},{default:a(()=>[e(u,{label:"手机号码",prop:"mobile"},{default:a(()=>[e(d,{modelValue:l(p).mobile,"onUpdate:modelValue":t[2]||(t[2]=o=>l(p).mobile=o),placeholder:"手机号码"},null,8,["modelValue"])]),_:1}),e(u,null,{default:a(()=>[e(g,{type:"primary",onClick:t[3]||(t[3]=o=>F(l(v)))},{default:a(()=>[b("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Ce=ie(ue,[["__scopeId","data-v-ee50a09e"]]);export{Ce as default};
