import{d as Z,r as _,a as w,z as ee,c as u,b as e,w as a,h as r,A as le,B as ae,e as ne,C as te,D as oe,o as s,f,i as v,F as b,G as F,H as q,I as T,E as g,J as re,K as se,L as ie,M as ue,p as de,s as pe,N as _e,v as ce,y as me,m as be,n as ge,l as ye,O as fe,P as ke,k as ve,q as we,_ as Te}from"./index-5837f9dc.js";/* empty css                  *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                  *//* empty css                    */import{_ as he,a as Ve}from"./plus-acd24b23.js";/* empty css               *//* empty css                  *//* empty css                        *//* empty css                        */const Ie={class:"header-box"},Ee={key:0},Be={key:1},Ce={key:2},xe={key:3},De=["src"],Ue=["src"],Fe={class:"el-upload-list__item-actions"},qe=["onClick"],Ae=Z({__name:"BannersView",setup(ze){const h=oe();let V=_([]);const I=w({merchantGuid:""});let d=_(!1),y=_("");const n=w({merchantGuid:"",bannerType:"",bannerImg:"",linkType:0,linkUrl:"",bannerDesc:"",guid:""});let A=_([{label:"logo图",value:"logo"},{label:"slogo图",value:"slogo"},{label:"my_ad图",value:"my_ad"}]),z=_([{label:"不跳转",value:0},{label:"跳转h5网页",value:1},{label:"跳转小程序内部页",value:2},{label:"跳转其他小程序",value:3}]);const E=_(),G=w({bannerType:[{required:!0,message:"请选择banner类型",trigger:"blur"}],bannerImg:[{required:!0,message:"请上传图片",trigger:"blur"}],linkType:[{required:!0,message:"请选择跳转类型",trigger:"blur"}]}),B=_(),L=t=>{let o=["image/jpg","image/png","image/jpeg"];if(o.includes(t.type)){if(t.size/1024/1024>2)return g.error("图片必须小于2M"),!1}else return g.warning("当前图片仅支持格式为："+o.join(" ，")),!1;return!0},N=async t=>{const o=new FormData;o.append("img",t.file);let i=await re(o);n.bannerImg=i.data},k=async()=>{I.merchantGuid=h.query.guid;let t=await ae(I);V.value=t.data},R=t=>{n.bannerImg="",B.value.clearFiles()},M=async t=>{await se({guid:t.guid}),g.success("删除成功"),k()},j=async t=>{n.merchantGuid=h.query.guid,t.validate(async o=>{if(o)try{y.value==="add"?(await ie(n),g.success("新增成功")):y.value==="edit"&&(await ue(n),g.success("修改成功")),t.resetFields(),d.value=!1,k()}catch(i){throw g.error(i),new Error(i)}})},$=t=>{console.log(t,"itemitem"),d.value=!0,n.guid=t.guid,n.bannerType=t.bannerType,n.bannerImg=t.bannerImg,n.linkType=t.linkType,n.bannerDesc=t.bannerDesc,y.value="edit"},O=()=>{d.value=!0,y.value="add"};return ee(()=>{k()}),k(),(t,o)=>{const i=de,c=pe,P=_e,S=ce,H=me,J=ne,C=be,x=ge,p=ye,K=he,D=fe,Q=Ve,W=ke,U=ve,X=we,Y=te;return s(),u("div",null,[e(J,{class:"wrapper"},{default:a(()=>[e(H,null,{default:a(()=>[f("div",Ie,[e(i,{type:"primary",onClick:O},{default:a(()=>[v("上传图片")]),_:1})]),e(S,{data:r(V),border:"",style:{width:"100%"}},{default:a(()=>[e(c,{prop:"sysId",label:"Id",width:"80"}),e(c,{prop:"typeText",label:"banner类型",width:"180"}),e(c,{prop:"bannerImg",label:"banner图片"},{default:a(l=>[e(P,{src:l.row.bannerImg,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src"])]),_:1}),e(c,{prop:"linkType",label:"跳转类型"},{default:a(l=>[l.row.linkType===0?(s(),u("span",Ee,"不跳转")):b("",!0),l.row.linkType===1?(s(),u("span",Be,"跳转h5网页")):b("",!0),l.row.linkType===2?(s(),u("span",Ce,"跳转小程序内部页面")):b("",!0),l.row.linkType===3?(s(),u("span",xe,"跳转其他小程序")):b("",!0)]),_:1}),e(c,{prop:"linkUrl",label:"跳转地址"}),e(c,{label:"操作"},{default:a(l=>[e(i,{size:"small",type:"primary",onClick:m=>$(l.row)},{default:a(()=>[v("编辑")]),_:2},1032,["onClick"]),e(i,{size:"small",type:"danger",onClick:m=>M(l.row)},{default:a(()=>[v("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(Y,{modelValue:r(d),"onUpdate:modelValue":o[5]||(o[5]=l=>le(d)?d.value=l:d=l),title:"修改/新增 banner",width:"600px"},{default:a(()=>[e(X,{ref_key:"addForm",ref:E,model:r(n),class:"demo-form-inline","label-width":"100px",rules:r(G)},{default:a(()=>[e(p,{label:"banner类型",prop:"bannerType"},{default:a(()=>[e(x,{modelValue:r(n).bannerType,"onUpdate:modelValue":o[0]||(o[0]=l=>r(n).bannerType=l),placeholder:"请选择"},{default:a(()=>[(s(!0),u(F,null,q(r(A),(l,m)=>(s(),T(C,{label:l.label,value:l.value,key:m},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"跳转类型",prop:"linkType"},{default:a(()=>[e(x,{modelValue:r(n).linkType,"onUpdate:modelValue":o[1]||(o[1]=l=>r(n).linkType=l),placeholder:"请选择"},{default:a(()=>[(s(!0),u(F,null,q(r(z),(l,m)=>(s(),T(C,{label:l.label,value:l.value,key:m},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(y)==="edit"?(s(),T(p,{key:0,label:"当前图片"},{default:a(()=>[r(n).bannerImg?(s(),u("img",{key:0,src:r(n).bannerImg,class:"edit-img"},null,8,De)):b("",!0)]),_:1})):b("",!0),e(p,{label:"图片",prop:"bannerImg"},{default:a(()=>[e(W,{ref_key:"uploadImgRef",ref:B,class:"avatar-uploader","list-type":"picture-card","before-upload":L,"http-request":N,limit:1},{file:a(({file:l})=>[f("div",null,[f("img",{class:"el-upload-list__item-thumbnail",src:l.url,alt:""},null,8,Ue),f("span",Fe,[f("span",{class:"el-upload-list__item-delete",onClick:m=>R(l)},[e(D,null,{default:a(()=>[e(Q)]),_:1})],8,qe)])])]),default:a(()=>[e(D,{size:"30"},{default:a(()=>[e(K)]),_:1})]),_:1},512)]),_:1}),e(p,{label:"跳转地址"},{default:a(()=>[e(U,{modelValue:r(n).linkUrl,"onUpdate:modelValue":o[2]||(o[2]=l=>r(n).linkUrl=l),placeholder:"跳转地址"},null,8,["modelValue"])]),_:1}),e(p,{label:"banner描述"},{default:a(()=>[e(U,{modelValue:r(n).bannerDesc,"onUpdate:modelValue":o[3]||(o[3]=l=>r(n).bannerDesc=l),placeholder:"banner描述"},null,8,["modelValue"])]),_:1}),e(p,null,{default:a(()=>[e(i,{type:"primary",onClick:o[4]||(o[4]=l=>j(r(E)))},{default:a(()=>[v("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Ke=Te(Ae,[["__scopeId","data-v-ba5a2508"]]);export{Ke as default};
