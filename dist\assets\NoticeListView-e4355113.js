import{d as re,a5 as de,r as g,a as R,a6 as me,by as ce,c as b,b as l,w as t,h as n,A as _e,cr as fe,E as h,e as ge,C as be,o as m,T as ye,I as k,f as S,G as C,H as x,i as u,U as j,F as Te,cs as ve,ct as Ve,cu as he,m as Ce,n as xe,l as je,p as Ue,q as De,s as Ee,ae as we,af as ke,v as Se,x as Ye,y as Ie,k as ze,al as Ne,am as Le,X as Me,Z as Ae,_ as Ge}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                     */const He={class:"header-box"},Re={class:"pagination-container"},qe={class:"dialog-footer"},Fe=re({__name:"NoticeListView",setup(Pe){const q=de(),Y=g(0),U=g(!1),s=R({noticeType:"",status:"",pageSize:10,page:1}),D=[{label:"全部",value:""},{label:"系统公告",value:1},{label:"活动公告",value:2},{label:"功能更新通知",value:3},{label:"维护通知",value:4}],F=[{label:"全部",value:""},{label:"启用",value:1},{label:"禁用",value:2}],I=[{label:"不跳转",value:0},{label:"跳转内部页面",value:1},{label:"跳转链接地址",value:2},{label:"跳转外部",value:3}];let z=g([]);const c=g(!1),y=g(""),T=g();let N=me(()=>q.getTenantInfo.guid);const i=R({guid:"",merchantGuid:"",noticeTitle:"",noticeContent:"",noticeType:1,noticeIcon:"",jumpType:0,jumpUrl:"",isTop:1,status:1,publishTime:"",expireTime:""}),P={noticeTitle:[{required:!0,message:"请输入公告标题",trigger:"blur"},{max:100,message:"标题长度不能超过100个字符",trigger:"blur"}],noticeContent:[{required:!0,message:"请输入公告内容",trigger:"blur"},{max:1e3,message:"内容长度不能超过1000个字符",trigger:"blur"}],noticeType:[{required:!0,message:"请选择公告类型",trigger:"change"}],jumpType:[{required:!0,message:"请选择跳转类型",trigger:"change"}],jumpUrl:[{validator:(a,o,r)=>{i.jumpType!==0&&!o?r(new Error('跳转类型不为"不跳转"时，跳转地址为必填')):r()},trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},_=async()=>{U.value=!0;try{const a=await fe({merchantGuid:""});z.value=a.data.data||[],Y.value=a.data.total||0}catch{h.error("获取公告列表失败")}finally{U.value=!1}},B=a=>{s.page=a,_()},O=a=>{s.pageSize=a,s.page=1,_()},$=()=>{s.page=1,_()},J=()=>{s.noticeType="",s.status="",s.page=1,_()},L=()=>{var a;Object.assign(i,{guid:"",merchantGuid:N.value,noticeTitle:"",noticeContent:"",noticeType:1,noticeIcon:"",jumpType:0,jumpUrl:"",isTop:1,status:1,publishTime:"",expireTime:""}),(a=T.value)==null||a.resetFields()},X=()=>{c.value=!0,y.value="ADD",L()},Z=a=>{c.value=!0,y.value="EDIT",Object.assign(i,{guid:a.guid,merchantGuid:a.merchantGuid,noticeTitle:a.noticeTitle,noticeContent:a.noticeContent,noticeType:a.noticeType,noticeIcon:a.noticeIcon,jumpType:a.jumpType,jumpUrl:a.jumpUrl,isTop:a.isTop,status:a.status,publishTime:a.publishTime,expireTime:a.expireTime})},K=async a=>{try{await ve({guid:a.guid}),h.success("删除成功"),_()}catch(o){console.error("删除失败:",o)}},Q=async()=>{if(T.value)try{await T.value.validate();const a={...i};a.merchantGuid=N.value,y.value==="ADD"?(await Ve(a),h.success("新增成功")):(await he(a),h.success("修改成功")),c.value=!1,_()}catch(a){console.error("提交失败:",a)}},M=()=>{c.value=!1,L()},W=a=>{const o=D.find(r=>r.value===a);return o?o.label:""},ee=a=>a===1?"启用":"禁用",le=a=>{const o=I.find(r=>r.value===a);return o?o.label:""};return ce(()=>{_()}),(a,o)=>{const r=Ce,v=xe,p=je,f=Ue,A=De,d=Ee,E=we,te=ke,ae=Se,oe=Ye,ne=Ie,ie=ge,w=ze,V=Ne,G=Le,H=Me,se=be,ue=Ae;return m(),b("div",null,[l(ie,{class:"wrapper"},{default:t(()=>[ye((m(),k(ne,null,{default:t(()=>[S("div",He,[l(A,{inline:!0,model:n(s),class:"demo-form-inline"},{default:t(()=>[l(p,{label:"公告类型"},{default:t(()=>[l(v,{modelValue:n(s).noticeType,"onUpdate:modelValue":o[0]||(o[0]=e=>n(s).noticeType=e),placeholder:"请选择公告类型",clearable:""},{default:t(()=>[(m(),b(C,null,x(D,e=>l(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"状态"},{default:t(()=>[l(v,{modelValue:n(s).status,"onUpdate:modelValue":o[1]||(o[1]=e=>n(s).status=e),placeholder:"请选择状态",clearable:""},{default:t(()=>[(m(),b(C,null,x(F,e=>l(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(p,null,{default:t(()=>[l(f,{type:"primary",onClick:$},{default:t(()=>[u("搜索")]),_:1}),l(f,{onClick:J},{default:t(()=>[u("重置")]),_:1}),l(f,{type:"primary",onClick:X},{default:t(()=>[u("新增公告")]),_:1})]),_:1})]),_:1},8,["model"])]),l(ae,{data:n(z),border:"",style:{width:"100%"}},{default:t(()=>[l(d,{prop:"sysId",label:"ID",width:"80"}),l(d,{prop:"noticeTitle",label:"公告标题","min-width":"150","show-overflow-tooltip":""}),l(d,{prop:"noticeContent",label:"公告内容","min-width":"200","show-overflow-tooltip":""}),l(d,{prop:"noticeType",label:"公告类型",width:"120"},{default:t(({row:e})=>[l(E,{type:e.noticeType===1?"info":e.noticeType===2?"success":e.noticeType===3?"warning":"danger"},{default:t(()=>[u(j(W(e.noticeType)),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"jumpType",label:"跳转类型",width:"120"},{default:t(({row:e})=>[u(j(le(e.jumpType)),1)]),_:1}),l(d,{prop:"jumpUrl",label:"跳转地址","min-width":"150","show-overflow-tooltip":""}),l(d,{prop:"isTop",label:"置顶",width:"80"},{default:t(({row:e})=>[l(E,{type:e.isTop===1?"success":"info"},{default:t(()=>[u(j(e.isTop===1?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"status",label:"状态",width:"80"},{default:t(({row:e})=>[l(E,{type:e.status===1?"success":"danger"},{default:t(()=>[u(j(ee(e.status)),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"publishTime",label:"发布时间",width:"160"}),l(d,{prop:"expireTime",label:"失效时间",width:"160"}),l(d,{label:"操作",width:"180",fixed:"right"},{default:t(({row:e})=>[l(f,{size:"small",type:"primary",onClick:pe=>Z(e)},{default:t(()=>[u("编辑")]),_:2},1032,["onClick"]),l(te,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"确定删除这条公告吗？",onConfirm:pe=>K(e)},{reference:t(()=>[l(f,{size:"small",type:"danger"},{default:t(()=>[u("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"]),S("div",Re,[l(oe,{"current-page":n(s).page,"onUpdate:currentPage":o[2]||(o[2]=e=>n(s).page=e),"page-size":n(s).pageSize,"onUpdate:pageSize":o[3]||(o[3]=e=>n(s).pageSize=e),"page-sizes":[10,20,50,100],total:n(Y),layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O,onCurrentChange:B},null,8,["current-page","page-size","total"])])]),_:1})),[[ue,n(U)]])]),_:1}),l(se,{modelValue:n(c),"onUpdate:modelValue":o[13]||(o[13]=e=>_e(c)?c.value=e:null),title:n(y)==="ADD"?"新增公告":"编辑公告",width:"600px",onClose:M},{footer:t(()=>[S("span",qe,[l(f,{onClick:M},{default:t(()=>[u("取消")]),_:1}),l(f,{type:"primary",onClick:Q},{default:t(()=>[u("确定")]),_:1})])]),default:t(()=>[l(A,{ref_key:"formRef",ref:T,model:n(i),rules:P,"label-width":"120px"},{default:t(()=>[l(p,{label:"公告标题",prop:"noticeTitle"},{default:t(()=>[l(w,{modelValue:n(i).noticeTitle,"onUpdate:modelValue":o[4]||(o[4]=e=>n(i).noticeTitle=e),placeholder:"请输入公告标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(p,{label:"公告内容",prop:"noticeContent"},{default:t(()=>[l(w,{modelValue:n(i).noticeContent,"onUpdate:modelValue":o[5]||(o[5]=e=>n(i).noticeContent=e),type:"textarea",rows:4,placeholder:"请输入公告内容",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(p,{label:"公告类型",prop:"noticeType"},{default:t(()=>[l(v,{modelValue:n(i).noticeType,"onUpdate:modelValue":o[6]||(o[6]=e=>n(i).noticeType=e),placeholder:"请选择公告类型"},{default:t(()=>[(m(!0),b(C,null,x(D.filter(e=>e.value!==""),e=>(m(),k(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"跳转类型",prop:"jumpType"},{default:t(()=>[l(v,{modelValue:n(i).jumpType,"onUpdate:modelValue":o[7]||(o[7]=e=>n(i).jumpType=e),placeholder:"请选择跳转类型"},{default:t(()=>[(m(),b(C,null,x(I,e=>l(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(i).jumpType!==0?(m(),k(p,{key:0,label:"跳转地址",prop:"jumpUrl"},{default:t(()=>[l(w,{modelValue:n(i).jumpUrl,"onUpdate:modelValue":o[8]||(o[8]=e=>n(i).jumpUrl=e),placeholder:"请输入跳转地址"},null,8,["modelValue"])]),_:1})):Te("",!0),l(p,{label:"是否置顶"},{default:t(()=>[l(G,{modelValue:n(i).isTop,"onUpdate:modelValue":o[9]||(o[9]=e=>n(i).isTop=e)},{default:t(()=>[l(V,{label:1},{default:t(()=>[u("是")]),_:1}),l(V,{label:0},{default:t(()=>[u("否")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"状态",prop:"status"},{default:t(()=>[l(G,{modelValue:n(i).status,"onUpdate:modelValue":o[10]||(o[10]=e=>n(i).status=e)},{default:t(()=>[l(V,{label:1},{default:t(()=>[u("启用")]),_:1}),l(V,{label:2},{default:t(()=>[u("禁用")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"发布时间"},{default:t(()=>[l(H,{modelValue:n(i).publishTime,"onUpdate:modelValue":o[11]||(o[11]=e=>n(i).publishTime=e),type:"datetime",placeholder:"选择发布时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(p,{label:"失效时间"},{default:t(()=>[l(H,{modelValue:n(i).expireTime,"onUpdate:modelValue":o[12]||(o[12]=e=>n(i).expireTime=e),type:"datetime",placeholder:"选择失效时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const nl=Ge(Fe,[["__scopeId","data-v-744dc885"]]);export{nl as default};
