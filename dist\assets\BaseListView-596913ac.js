import{d as ae,r as m,a as C,c as G,b as e,w as l,h as o,A as te,cm as le,E as _,e as re,C as oe,o as V,f as h,i as d,T as se,I as ne,U as z,G as ie,H as de,cn as ue,co as pe,cp as ce,p as ge,l as me,q as _e,s as fe,ae as ye,af as be,v as ke,x as we,y as ve,k as Pe,m as Ve,n as he,a3 as Te,al as xe,am as De,Z as Ee,_ as Ce}from"./index-2d10794a.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                   *//* empty css                     */const Ge={class:"search-box"},ze={class:"pagination-container"},Ne={class:"dialog-footer"},Ue=ae({__name:"BaseListView",setup(Ae){const b=m(0),k=m(!1),u=C({merchantGuid:"",pageSize:100,page:1}),w=m([]),c=m(!1),v=m(""),P=m(),y=m("add"),s=C({packageGuid:"",merchantGuid:"",packageName:"",packageType:1,durationDays:30,salePrice:0,originalPrice:0,packageIcon:"",rewardPoints:0,sortOrder:1,status:1,packageDesc:""}),N={packageName:[{required:!0,message:"请输入套餐名称",trigger:"blur"}],packageType:[{required:!0,message:"请选择套餐类型",trigger:"change"}],durationDays:[{required:!0,message:"请输入有效天数",trigger:"blur"}],salePrice:[{required:!0,message:"请输入售价",trigger:"blur"}],originalPrice:[{required:!0,message:"请输入原价",trigger:"blur"}],rewardPoints:[{required:!0,message:"请输入奖励积分",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],packageDesc:[{required:!0,message:"请输入套餐说明",trigger:"blur"}]},U=[{label:"月卡",value:1},{label:"季卡",value:2},{label:"年卡",value:3},{label:"自定义",value:4}],g=async()=>{k.value=!0;try{const t=await le(u);t.data&&Array.isArray(t.data)?(w.value=t.data,b.value=t.data.length):t.data&&t.data.data&&(w.value=t.data.data,b.value=t.data.total)}catch{_.error("获取套餐列表失败")}finally{k.value=!1}},A=()=>{u.page=1,g()},O=()=>{u.page=1,g()},S=t=>{u.page=t,g()},I=t=>{u.pageSize=t,u.page=1,g()},q=t=>({1:"success",2:"warning",3:"info"})[t]||"",B=t=>t===1?"success":"danger",L=()=>{y.value="add",v.value="新增套餐",Object.assign(s,{packageGuid:"",merchantGuid:"",packageName:"",packageType:1,durationDays:30,salePrice:0,originalPrice:0,packageIcon:"",rewardPoints:0,sortOrder:1,status:1,packageDesc:""}),c.value=!0},R=t=>{y.value="edit",v.value="编辑套餐",Object.assign(s,{packageGuid:t.guid,merchantGuid:t.merchantGuid,packageName:t.packageName,packageType:t.packageType,durationDays:t.durationDays,salePrice:t.salePrice,originalPrice:t.originalPrice,packageIcon:t.packageIcon,rewardPoints:t.rewardPoints,sortOrder:t.sortOrder,status:t.status,packageDesc:t.packageDesc}),c.value=!0},M=async t=>{try{await ue({packageGuid:t.guid,merchantGuid:""}),_.success("删除成功"),g()}catch{_.error("删除失败")}},j=async()=>{P.value&&await P.value.validate(async t=>{if(t)try{const r={...s,salePrice:s.salePrice,originalPrice:s.originalPrice,merchantGuid:s.merchantGuid||"e108201b02ae42e686bcc4c302cbbd11"};if(y.value==="edit")await pe(r),_.success("更新成功");else{const{packageGuid:p,...n}=r;await ce(n),_.success("创建成功")}c.value=!1,g()}catch{_.error(y.value==="edit"?"更新失败":"创建失败")}})};return g(),(t,r)=>{const p=ge,n=me,T=_e,i=fe,x=ye,F=be,$=ke,Y=we,H=ve,Z=re,D=Pe,J=Ve,K=he,f=Te,E=xe,Q=De,W=oe,X=Ee;return V(),G("div",null,[e(Z,{class:"wrapper"},{default:l(()=>[e(H,null,{default:l(()=>[h("div",Ge,[e(T,{inline:!0,class:"search-form"},{default:l(()=>[e(n,null,{default:l(()=>[e(p,{type:"primary",onClick:A},{default:l(()=>[d("搜索")]),_:1}),e(p,{onClick:O},{default:l(()=>[d("重置")]),_:1}),e(p,{type:"success",onClick:L},{default:l(()=>[d("新增套餐")]),_:1})]),_:1})]),_:1})]),se((V(),ne($,{data:o(w),border:"",style:{width:"100%"}},{default:l(()=>[e(i,{prop:"sysId",label:"ID",width:"80"}),e(i,{prop:"packageName",label:"套餐名称","min-width":"150"}),e(i,{prop:"packageDesc",label:"套餐描述","min-width":"200","show-overflow-tooltip":""}),e(i,{prop:"packageTypeText",label:"套餐类型",width:"100"},{default:l(a=>[e(x,{type:q(a.row.packageType)},{default:l(()=>[d(z(a.row.packageTypeText),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"durationDays",label:"有效期(天)",width:"100"}),e(i,{prop:"originalPriceYuan",label:"原价(元)",width:"100"}),e(i,{prop:"salePriceYuan",label:"售价(元)",width:"100"}),e(i,{prop:"rewardPointsText",label:"奖励积分",width:"100"}),e(i,{prop:"statusText",label:"状态",width:"80"},{default:l(a=>[e(x,{type:B(a.row.status)},{default:l(()=>[d(z(a.row.statusText),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"createTimeText",label:"创建时间",width:"180"}),e(i,{fixed:"right",label:"操作",width:"150"},{default:l(a=>[e(p,{size:"small",type:"primary",onClick:ee=>R(a.row)},{default:l(()=>[d("编辑")]),_:2},1032,["onClick"]),e(F,{title:"确认删除该套餐吗？","confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:ee=>M(a.row)},{reference:l(()=>[e(p,{size:"small",type:"danger"},{default:l(()=>[d("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[X,o(k)]]),h("div",ze,[e(Y,{"current-page":o(u).page,"onUpdate:currentPage":r[0]||(r[0]=a=>o(u).page=a),"page-size":o(u).pageSize,"onUpdate:pageSize":r[1]||(r[1]=a=>o(u).pageSize=a),"page-sizes":[10,20,50,100],total:o(b),layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页",onSizeChange:I,onCurrentChange:S},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(W,{title:o(v),modelValue:o(c),"onUpdate:modelValue":r[12]||(r[12]=a=>te(c)?c.value=a:null),width:"600px"},{footer:l(()=>[h("span",Ne,[e(p,{onClick:r[11]||(r[11]=a=>c.value=!1)},{default:l(()=>[d("取消")]),_:1}),e(p,{type:"primary",onClick:j},{default:l(()=>[d("确定")]),_:1})])]),default:l(()=>[e(T,{ref_key:"formRef",ref:P,model:o(s),rules:N,"label-width":"100px"},{default:l(()=>[e(n,{label:"套餐名称",prop:"packageName"},{default:l(()=>[e(D,{modelValue:o(s).packageName,"onUpdate:modelValue":r[2]||(r[2]=a=>o(s).packageName=a),placeholder:"请输入套餐名称"},null,8,["modelValue"])]),_:1}),e(n,{label:"套餐类型",prop:"packageType"},{default:l(()=>[e(K,{modelValue:o(s).packageType,"onUpdate:modelValue":r[3]||(r[3]=a=>o(s).packageType=a),placeholder:"请选择套餐类型",style:{width:"100%"}},{default:l(()=>[(V(),G(ie,null,de(U,a=>e(J,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"有效天数",prop:"durationDays"},{default:l(()=>[e(f,{modelValue:o(s).durationDays,"onUpdate:modelValue":r[4]||(r[4]=a=>o(s).durationDays=a),min:1,max:9999,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"售价(元)",prop:"salePrice"},{default:l(()=>[e(f,{modelValue:o(s).salePrice,"onUpdate:modelValue":r[5]||(r[5]=a=>o(s).salePrice=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"原价(元)",prop:"originalPrice"},{default:l(()=>[e(f,{modelValue:o(s).originalPrice,"onUpdate:modelValue":r[6]||(r[6]=a=>o(s).originalPrice=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"奖励积分",prop:"rewardPoints"},{default:l(()=>[e(f,{modelValue:o(s).rewardPoints,"onUpdate:modelValue":r[7]||(r[7]=a=>o(s).rewardPoints=a),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"排序",prop:"sortOrder"},{default:l(()=>[e(f,{modelValue:o(s).sortOrder,"onUpdate:modelValue":r[8]||(r[8]=a=>o(s).sortOrder=a),min:0,max:999,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"状态",prop:"status"},{default:l(()=>[e(Q,{modelValue:o(s).status,"onUpdate:modelValue":r[9]||(r[9]=a=>o(s).status=a)},{default:l(()=>[e(E,{label:1},{default:l(()=>[d("启用")]),_:1}),e(E,{label:2},{default:l(()=>[d("禁用")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"套餐说明",prop:"packageDesc"},{default:l(()=>[e(D,{modelValue:o(s).packageDesc,"onUpdate:modelValue":r[10]||(r[10]=a=>o(s).packageDesc=a),type:"textarea",rows:3,maxlength:"30","show-word-limit":"",placeholder:"请输入套餐说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});const Je=Ce(Ue,[["__scopeId","data-v-186c9141"]]);export{Je as default};
