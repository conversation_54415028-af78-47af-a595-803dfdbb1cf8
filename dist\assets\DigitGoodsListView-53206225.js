import{d as B,r as p,a as D,c as F,b as e,w as t,$ as G,e as I,o as m,T as P,h as l,I as h,f as A,i as _,U as L,F as U,a0 as q,W as z,m as M,n as $,l as O,p as R,q as W,s as Y,v as Z,y as j,x as H,Y as J,Z as K}from"./index-5837f9dc.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     *//* empty css                       *//* empty css                   */const Q={class:"hearder-box"},pe=B({__name:"DigitGoodsListView",setup(X){const u=p(0),c=p(!1),n=D({orderNo:"",orderStatus:"",merchantGuid:"",orderTime:"",pageSize:10,page:1});let f=p([]);const d=async()=>{c.value=!0;let a=await G(n);c.value=!1,u.value=a.data.total,f.value=a.data.data},y=async a=>{n.page=a,d()},v=()=>{d()};d();const w=async a=>{let r=await q({orderNo:a.orderNo});console.log(r,"resres");let s=r.data.isPay?"已成功支付":"未支付";z.alert(s,"查询结果",{confirmButtonText:"确定"})};return(a,r)=>{const s=M,x=$,g=O,b=R,C=W,o=Y,E=Z,k=j,T=H,N=J,S=I,V=K;return m(),F("div",null,[e(S,{class:"wrapper"},{default:t(()=>[P((m(),h(k,null,{default:t(()=>[A("div",Q,[e(C,{inline:!0,model:l(n),class:"demo-form-inline"},{default:t(()=>[e(g,{label:"支付状态"},{default:t(()=>[e(x,{modelValue:l(n).orderStatus,"onUpdate:modelValue":r[0]||(r[0]=i=>l(n).orderStatus=i),placeholder:"请选择"},{default:t(()=>[e(s,{label:"全部",value:""}),e(s,{label:"待支付",value:"100"}),e(s,{label:"已支付",value:"200"})]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:t(()=>[e(b,{type:"primary",onClick:v},{default:t(()=>[_("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),e(E,{data:l(f),border:"",style:{width:"100%"}},{default:t(()=>[e(o,{prop:"platformUserSysId",label:"订单Id",width:"80"}),e(o,{prop:"user.nickname",label:"用户昵称",width:"180"}),e(o,{prop:"orderNo",label:"订单编号"}),e(o,{prop:"payAmount",label:"支付金额",width:"100"}),e(o,{prop:"statusText",label:"支付状态",width:"160"},{default:t(i=>[_(L(i.row.statusText)+" ",1),i.row.statusText==="待支付"?(m(),h(b,{key:0,size:"small",type:"primary",onClick:ee=>w(i.row)},{default:t(()=>[_("查询")]),_:2},1032,["onClick"])):U("",!0)]),_:1}),e(o,{prop:"createTime",label:"下单时间"}),e(o,{prop:"modifyTime",label:"付款时间"})]),_:1},8,["data"])]),_:1})),[[V,l(c)]]),e(N,null,{default:t(()=>[e(T,{background:"",layout:"prev,pager, next",total:l(u),"current-page":l(n).page,onCurrentChange:y},null,8,["total","current-page"])]),_:1})]),_:1})])}}});export{pe as default};
