import{d as D,r as f,a as R,c as p,b as t,w as o,h as l,A as b,D as A,e as L,C as M,o as i,f as e,i as d,G as y,H as V,U as c,F as q,I as G,E as H,b7 as P,k as $,p as j,b8 as J,b9 as O,ba as Q,y as W,b6 as X,ae as Y,t as Z,bb as ee,bc as te,_ as se}from"./index-5837f9dc.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                *//* empty css               *//* empty css                    *//* empty css                 */const E=m=>(ee("data-v-746b1343"),m=m(),te(),m),oe=E(()=>e("div",{class:"test-title"},[e("div",{class:"h1"},"召回测试"),e("div",{class:"h2"},"基于给定的查询文本测试知识库的召回效果。")],-1)),le={class:"test-input-box"},ae=E(()=>e("div",{class:"title-box"},[e("h6",null,"源文本")],-1)),ne={class:"content"},de={class:"footer"},ie={class:"result-box"},ce={class:"list-box"},_e=["onClick"],re={class:"content"},ue={key:0,class:"tip"},pe={class:"text-paragraph-box"},me={class:"content"},he={class:"keywords"},ve={class:"title"},we={class:"item-box"},fe={class:"detail"},ge=D({__name:"KnowledgeTestView",setup(m){const k=A().query.guid,h=f(!1),_=f(""),w=f([]),T=async()=>{if(_.value.trim().length===0){H.warning("请输入源文本");return}let r=await P({guid:k,keywords:_.value});r.data.records.forEach(n=>{n.scoreFormat=n.score.toFixed(2)}),w.value=r.data.records};let a=R({word_count:0,enabled:!1,content:"",keywords:[],index_node_hash:"",hit_count:0});const C=r=>{h.value=!0,a={fileItem:a,...r.segment}};return(r,n)=>{const g=$,I=j,x=J,S=O,z=Q,B=W,F=L,u=X,U=Y,K=Z,N=M;return i(),p("div",null,[t(F,{class:"wrapper"},{default:o(()=>[t(B,null,{default:o(()=>[oe,t(z,{gutter:24},{default:o(()=>[t(x,{span:12},{default:o(()=>[e("div",le,[ae,e("div",ne,[t(g,{modelValue:l(_),"onUpdate:modelValue":n[0]||(n[0]=s=>b(_)?_.value=s:null),"input-style":{width:"100%"},rows:18,placeholder:"请输入文本，建议使用简短的陈述句",type:"textarea","show-word-limit":"",maxlength:"200",resize:"none"},null,8,["modelValue"])]),e("div",de,[t(I,{type:"primary",onClick:T},{default:o(()=>[d("测试")]),_:1})])])]),_:1}),t(x,{span:12},{default:o(()=>[e("div",ie,[e("div",ce,[(i(!0),p(y,null,V(l(w),s=>(i(),p("div",{class:"item",onClick:v=>C(s),key:s.segment.id},[t(S,{percentage:s.score.toFixed(2)*100,format:v=>(v/100).toString(),color:"#d0d5dd"},null,8,["percentage","format"]),e("div",re,c(s.segment.content),1)],8,_e))),128))]),l(w).length==0?(i(),p("div",ue,"召回测试结果将展示在这里")):q("",!0)])]),_:1})]),_:1})]),_:1})]),_:1}),t(N,{modelValue:l(h),"onUpdate:modelValue":n[3]||(n[3]=s=>b(h)?h.value=s:null),title:"文档段落",width:"600"},{default:o(()=>[e("div",pe,[e("div",me,[t(g,{"input-style":{width:"100%"},modelValue:l(a).content,"onUpdate:modelValue":n[1]||(n[1]=s=>l(a).content=s),rows:20,disabled:"",type:"textarea",resize:"none"},null,8,["modelValue"])]),e("div",he,[e("div",ve,[t(u,{size:"small"},{default:o(()=>[d("关键词")]),_:1})]),e("div",we,[(i(!0),p(y,null,V(l(a).keywords,(s,v)=>(i(),G(U,{key:v},{default:o(()=>[d(c(s),1)]),_:2},1024))),128))])]),e("div",fe,[t(u,{class:"mx-1",size:"small"},{default:o(()=>[d(c(l(a).word_count)+"字符",1)]),_:1}),t(u,{class:"mx-1",size:"small"},{default:o(()=>[d(c(l(a).hit_count)+"召回次数",1)]),_:1}),t(u,{class:"mx-3",size:"small",truncated:""},{default:o(()=>[d("向量哈希:"+c(l(a).index_node_hash),1)]),_:1}),e("div",null,[t(u,{class:"status",size:"small"},{default:o(()=>[d(c(l(a).enabled?"已启用":"未启用"),1)]),_:1}),t(K,{modelValue:l(a).enabled,"onUpdate:modelValue":n[2]||(n[2]=s=>l(a).enabled=s),disabled:""},null,8,["modelValue"])])])])]),_:1},8,["modelValue"])])}}});const ze=se(ge,[["__scopeId","data-v-746b1343"]]);export{ze as default};
