import { createRouter, createWebHistory } from 'vue-router';
import HomeView from '../views/HomeView.vue';
import NotFound from '@/views/404.vue';

//商家列表
const TenantListView = () => import('../components/tenant/TenantListView.vue');

//管理员列表
const SetBannerView = () => import('../components/tenant/BannersView.vue');
// 聊天产品订单
const GoodsListView = () => import('../components/goods/GoodsListVIew.vue');
//数字人订单
const DigitGoodsListView = () => import('../components/digit-goods/DigitGoodsListView.vue');
//会员列表
const VipListView = () => import('../components/vip/ListView.vue');
//聊天收藏
const CollectListView = () => import('../components/collect/ListView.vue');
//管理员列表
const AdminListView = () => import('../components/admin/ListView.vue');
//系统设置
const SetSystemInfoView = () => import('../components/tenant/SetSystemInfoView.vue');
// 商家聊天订单列表
const ChatGoodsView = () => import('../components/tenant/ChatGoodsView.vue');
// 数字人列表
const DigitListView = () => import('../components/vip/DigitListView.vue');
// ai好友列表
const RolelistView = () => import('../components/role/AiRoleListView.vue');
// 场景管理
const SceneListView = () => import('../components/scene/SceneListView.vue');
// 绘画列表
const DrawListView = () => import('../components/tool/DrawListView.vue');
// 视频列表
const VideoListView = () => import('../components/tool/VideoListView.vue');
// 素材管理
const MaterListView = () => import('../components/matter/MatterListView.vue');
// 首页导航配置
const NavListView = () => import('../components/share/NavListView.vue');
// 分身列表
const CardListView = () => import('../components/card/CardListView.vue');
// 海报列表
const PosterListView = () => import('../components/poster/PosterListView.vue');

// 知识库列表
const KnowledgeListView = () => import('../components/knowledge/KnowledgeListView.vue');
// 知识库详情
const KnowledgeDetailView = () => import('../components/knowledge/KnowledgeDetailView.vue');
// 知识库文档列表
const KnowledgeFileDetailView = () => import('../components/knowledge/KnowledgeFileDetailView.vue');
// 召回测试
const KnowledgeTestView = () => import('../components/knowledge/KnowledgeTestView.vue');
// 会员卡列表
const vipListView = () => import('../components/vip-card/vipListView.vue');
// 大会员申请列表
const applyListView = () => import('../components/vip-card/applyListView.vue');
// 大会员会员列表
const userListView = () => import('../components/vip-card/userListView.vue');
// 大会员订单列表
const orderListView = () => import('../components/vip-card/orderListView.vue');
// 大会员提现记录
const withdrawalListView = () => import('../components/vip-card/withdrawalListView.vue');
// 大会员配置
const vipConfigView = () => import('../components/vip-card/vipConfigView.vue');
// AI主页列表
const expoListView = () => import('../components/expo/expoListView.vue');
// AI主页我的主页
const myExpoListView = () => import('../components/expo/expoMyExpoListView.vue');
// AI主页编辑新增
const setExpoInfoView = () => import('../components/expo/expoEditView.vue');
// AI主页问题列表
const expoFaqListView = () => import('../components/expo/expoFaqListView.vue');
// AI主页banner列表
const expoBannerView = () => import('../components/expo/expoBannerView.vue');
// AI主页列表
const tenantExpoListView = () => import('../components/tenant/expoListView.vue');
// AI主页管理员列表
const ExpoAdminListView = () => import('../components/expo/adminView.vue');
// AI主页兑换码列表
const redeemCodesView = () => import('../components/expo/redeemCodes.vue');
// 智能体列表
const agentListView = () => import('../components/agents/ListView.vue');
// 角色列表
const PermissionRoleListView = () => import('../components/permission/RoleListView.vue');
// 欢迎页
const indexView = () => import('../components/index/index.vue');
// 付费智能体列表
const payAgentClassListView = () => import('../components/pay-agent/ClassListView.vue');
// 用户智能体列表
const UserAgentListView = () => import('../components/pay-agent/UserAgentList.vue');
// 付费智能体配置
const AgentConfigListView = () => import('../components/pay-agent/ConfigListView.vue');
// 付费智能体会员列表
const VipAgentListView = () => import('../components/pay-agent/VipListView.vue');
// 付费智能体基础会员套餐
const vipBasePackageListView = () => import('../components/pay-agent-packages/BaseListView.vue');
// 精品智能体订单列表
const BoutiqueVipListView = () => import('../components/pay-agent/BoutiqueVipListView.vue');
// 系统公告列表
const NoticeListView = () => import('../components/pay-agent/NoticeListView.vue');
// 提现列表
const WithdrawListView = () => import('../components/pay-agent/WithdrawListView.vue');
// 付费智能体广告图管理
const BannerView = () => import('../components/pay-agent/BannerView.vue');
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/index',
      component: HomeView,
      children: [
        {
          name: 'index',
          path: '/index',
          component: indexView,
          meta: {
            title: '首页',
          },
        },
        {
          name: 'tenantList',
          path: '/tenant/list',
          component: TenantListView,
          meta: {
            title: '商户列表',
          },
        },
        {
          name: 'bannerList',
          path: '/tenant/banner',
          component: SetBannerView,
          meta: {
            title: 'banner列表',
          },
        },
        {
          name: 'setSystem',
          path: '/tenant/system',
          component: SetSystemInfoView,
          meta: {
            title: '系统配置',
          },
        },
        {
          name: 'tenantChats',
          path: '/tenant/chats',
          component: ChatGoodsView,
          meta: {
            title: '商家商品',
          },
        },

        {
          name: 'goodsList',
          path: '/goods/list',
          component: GoodsListView,
          meta: {
            title: '聊天产品列表',
          },
        },
        {
          name: 'digitGoodsList',
          path: '/digit-goods/list',
          component: DigitGoodsListView,
          meta: {
            title: '数字人订单',
          },
        },
        {
          name: 'vipList',
          path: '/vip/list',
          component: VipListView,
          meta: {
            title: '会员列表',
          },
        },
        {
          name: 'digitList',
          path: '/vip/digitList',
          component: DigitListView,
          meta: {
            title: '数字人列表',
          },
        },
        {
          name: 'collectList',
          path: '/collect/list',
          component: CollectListView,
          meta: {
            title: '聊天收藏',
          },
        },
        {
          name: 'admin',
          path: '/admin/list',
          component: AdminListView,
          meta: {
            title: '管理员列表',
          },
        },
        {
          name: 'aiRole',
          path: '/role/rolelist',
          component: RolelistView,
          meta: {
            title: 'Ai好友列表',
          },
        },
        {
          name: 'sceneList',
          path: '/scene/scenelist',
          component: SceneListView,
          meta: {
            title: '场景列表',
          },
        },
        {
          name: 'drawlist',
          path: '/tool/drawlist',
          component: DrawListView,
          meta: {
            title: '绘画列表',
          },
        },
        {
          name: 'videolist',
          path: '/tool/videolist',
          component: VideoListView,
          meta: {
            title: '视频列表',
          },
        },
        {
          name: 'setting',
          path: '/setting/matter',
          component: MaterListView,
          meta: {
            title: '素材管理',
          },
        },
        {
          name: 'navlist',
          path: '/share/navlist',
          component: NavListView,
          meta: {
            title: '首页分享配置',
          },
        },
        {
          name: 'posterlist',
          path: '/poster/list',
          component: PosterListView,
          meta: {
            title: '海报列表',
          },
        },
        {
          name: 'cardlist',
          path: '/card/cardlist',
          component: CardListView,
          meta: {
            title: '海报列表',
          },
        },
        {
          name: 'knowledgelist',
          path: '/knowledge/knowledgelist',
          component: KnowledgeListView,
          meta: {
            title: '知识库列表',
          },
        },
        {
          name: 'knowledgedetail',
          path: '/knowledge/knowledgedetail',
          component: KnowledgeDetailView,
          meta: {
            title: '知识库文档列表',
          },
        },
        {
          name: 'knowledgefiledetail',
          path: '/knowledge/knowledgefiledetail',
          component: KnowledgeFileDetailView,
          meta: {
            title: '知识库文档片段列表',
          },
        },
        {
          name: 'KnowledgeTestView',
          path: '/knowledge/KnowledgeTestView',
          component: KnowledgeTestView,
          meta: {
            title: '召回测试',
          },
        },
        {
          name: 'vipCardList',
          path: '/vip-card/list',
          component: vipListView,
          meta: {
            title: '会员卡列表',
          },
        },
        {
          name: 'cardApplylist',
          path: '/vip-card/applylist',
          component: applyListView,
          meta: {
            title: '大会员申请列表',
          },
        },
        {
          name: 'cardUserlist',
          path: '/vip-card/userlist',
          component: userListView,
          meta: {
            title: '大会员列表',
          },
        },
        {
          name: 'cardOrderList',
          path: '/vip-card/orderlist',
          component: orderListView,
          meta: {
            title: '大会员订单列表',
          },
        },
        {
          name: 'cardWithdrawalList',
          path: '/vip-card/withdrawalList',
          component: withdrawalListView,
          meta: {
            title: '大会员提现列表',
          },
        },
        {
          name: 'bigVipConfig',
          path: '/vip-card/bigVipConfig',
          component: vipConfigView,
          meta: {
            title: '大会员配置',
          },
        },
        {
          name: 'myExpoList',
          path: '/expo/myexpoList',
          component: myExpoListView,
          meta: {
            title: '我的AI主页',
          },
        },
        {
          name: 'expoList',
          path: '/expo/expoList',
          component: expoListView,
          meta: {
            title: 'AI主页列表',
          },
        },
        {
          name: 'tenantExpoList',
          path: '/tenant/tenantExpoList',
          component: tenantExpoListView,
          meta: {
            title: 'AI主页列表',
          },
        },
        {
          name: 'setExpoInfo',
          path: '/expo/setExpoInfo',
          component: setExpoInfoView,
          meta: {
            title: '编辑AI主页',
          },
        },
        {
          name: 'expoFaqList',
          path: '/expo/expoFaqList',
          component: expoFaqListView,
          meta: {
            title: 'AI主页常见问题',
          },
        },
        {
          name: 'expoBannerList',
          path: '/expo/expoBannerList',
          component: expoBannerView,
          meta: {
            title: 'banner图设置',
          },
        },
        {
          name: 'expoAdmin',
          path: '/expo/adminView',
          component: ExpoAdminListView,
          meta: {
            title: 'AI主页管理员列表',
          },
        },
        {
          name: 'redeemCodes',
          path: '/expo/redeemCodes',
          component: redeemCodesView,
          meta: {
            title: '邀请码列表',
          },
        },
        {
          name: 'agentList',
          path: '/agents/list',
          component: agentListView,
          meta: {
            title: '智能体列表',
          },
        },
        {
          name: 'roleList',
          path: '/permissionrole/list',
          component: PermissionRoleListView,
          meta: {
            title: '角色列表',
          },
        },
        {
          name: 'payAgentClassList',
          path: '/pay-agent/classlist',
          component: payAgentClassListView,
          meta: {
            title: '智能体分类列表',
          },
        },
        {
          name: 'userAgentList',
          path: '/pay-agent/useragentlist',
          component: UserAgentListView,
          meta: {
            title: '用户智能体列表',
          },
        },
        {
          name: 'payAgentConfig',
          path: '/pay-agent/config',
          component: AgentConfigListView,
          meta: {
            title: '付费智能体配置',
          },
        },
        {
          name: 'vipAgentList',
          path: '/pay-agent/vipagentlist',
          component: VipAgentListView,
          meta: {
            title: '会员智能体订单列表',
          },
        },
        {
          name: 'bputiquevipagentlist',
          path: '/pay-agent/bputiquevipagentlist',
          component: BoutiqueVipListView,
          meta: {
            title: '精品智能体订单列表',
          },
        },
        {
          name: 'vipBasePackageList',
          path: '/pay-agent-packages/vipbasepackageList',
          component: vipBasePackageListView,
          meta: {
            title: '会员基础套餐列表',
          },
        },
        {
          name: 'noticeList',
          path: '/pay-agent/noticelist',
          component: NoticeListView,
          meta: {
            title: '系统公告列表',
          },
        },
        {
          name: 'withdrawlist',
          path: '/pay-agent/withdrawlist',
          component: WithdrawListView,
          meta: {
            title: '提现列表',
          },
        },
        {
          name: 'payagentbannerList',
          path: '/pay-agent/banner',
          component: BannerView,
          meta: {
            title: '付费智能体广告图管理',
          },
        },
      ],
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: {
        title: '登录',
      },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: NotFound,
      meta: {
        title: '404错误啦',
      },
    },
    // {
    // path: '/about',
    // name: 'about',
    // route level code-splitting
    // this generates a separate chunk (About.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    // component: () => import('../views/AboutView.vue')
    // },
  ],
});

export default router;

// {
//           name: 'tenantAdmin',
//           path: '/tenant/tenantAdmin',
//           component: TenantAdminListView,
//           meta: {
//             title: '商户管理员列表',
//           },
//         },
