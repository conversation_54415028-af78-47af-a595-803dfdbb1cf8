import{d as A,r,a as u,c as m,b as a,w as l,cv as L,c4 as I,E as S,e as P,o as p,T as N,h as n,I as y,i as T,U as V,f as W,s as k,N as B,ae as D,v as U,x as G,y as M,Z as O,_ as j}from"./index-5837f9dc.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                        */const Z={key:1},q={class:"pagination-container"},F=A({__name:"WithdrawListView",setup(H){const d=r(0),i=r(!1),t=u({merchantGuid:"",pageSize:10,page:1});let g=r([]);r(!1),r(""),r(),r("add"),u({guid:"",merchantGuid:"",categoryName:"",sortOrder:1,status:1}),(async()=>{i.value=!0;try{let e=await L(t);console.log(e,"-------------------dsadas")}catch{}finally{i.value=!1}})();const _=async()=>{i.value=!0;try{let e=await I(t);e.data&&Array.isArray(e.data)?(g.value=e.data,d.value=e.data.length):e.data&&e.data.data&&(g.value=e.data.data,d.value=e.data.total)}catch{S.error("获取分类列表失败")}finally{i.value=!1}},h=e=>{t.page=e,_()},w=e=>{t.pageSize=e,t.page=1,_()};return _(),(e,c)=>{const s=k,f=B,v=D,b=U,x=G,z=M,C=P,E=O;return p(),m("div",null,[a(C,{class:"wrapper"},{default:l(()=>[a(z,null,{default:l(()=>[N((p(),y(b,{data:n(g),border:"",style:{width:"100%"}},{default:l(()=>[a(s,{prop:"sysId",label:"ID"}),a(s,{prop:"categoryName",label:"分类名称"}),a(s,{prop:"categoryDesc",label:"分类描述","show-overflow-tooltip":""}),a(s,{prop:"categoryIcon",label:"分类图标",width:"100"},{default:l(o=>[o.row.categoryIcon?(p(),y(f,{key:0,src:o.row.categoryIcon,style:{width:"40px",height:"40px"},fit:"cover"},null,8,["src"])):(p(),m("span",Z,"-"))]),_:1}),a(s,{prop:"sortOrder",label:"排序"}),a(s,{prop:"status",label:"状态",width:"100"},{default:l(o=>[a(v,{type:o.row.status===1?"success":"danger"},{default:l(()=>[T(V(o.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(s,{prop:"createTime",label:"创建时间"})]),_:1},8,["data"])),[[E,n(i)]]),W("div",q,[a(x,{"current-page":n(t).page,"onUpdate:currentPage":c[0]||(c[0]=o=>n(t).page=o),"page-size":n(t).pageSize,"onUpdate:pageSize":c[1]||(c[1]=o=>n(t).pageSize=o),"page-sizes":[10,20,50,100],total:n(d),layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页",onSizeChange:w,onCurrentChange:h},null,8,["current-page","page-size","total"])])]),_:1})]),_:1})])}}});const te=j(F,[["__scopeId","data-v-7363512c"]]);export{te as default};
