<script setup lang="ts">
import { getSystemNoticeListApi, createSystemNoticeListApi, updateSystemNoticeListApi, deleteSystemNoticeListApi } from '@/api';
import { useAdminCommonStore } from '@/stores/adminCommon';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';

const store = useAdminCommonStore();

// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  noticeType: '',
  status: '',
  pageSize: 10,
  page: 1,
});

// 公告类型配置
const noticeTypes = [
  { label: '全部', value: '' },
  { label: '系统公告', value: 1 },
  { label: '活动公告', value: 2 },
  { label: '功能更新通知', value: 3 },
  { label: '维护通知', value: 4 },
];

// 状态配置
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
];

// 跳转类型配置
const jumpTypes = [
  { label: '不跳转', value: 0 },
  { label: '跳转小程序内部页面(小程序路径)', value: 1 },
  { label: '跳转网页(链接)', value: 2 },
  { label: '跳转其他小程序(小程序appid)', value: 3 },
];

// 列表数据
let noticeList = ref([]);

// 弹窗相关
const dialogVisible = ref(false);
const dialogType = ref('');
const formRef = ref<FormInstance>();

// 获取商户GUID
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});

// 表单数据
const formData = reactive({
  guid: '',
  merchantGuid: '',
  noticeTitle: '',
  noticeContent: '',
  noticeType: 1,
  noticeIcon: '',
  jumpType: 0,
  jumpUrl: '',
  isTop: 1,
  status: 1,
  publishTime: '',
  expireTime: '',
});

// 表单验证规则
const formRules: FormRules = {
  noticeTitle: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
  ],
  noticeContent: [
    { required: true, message: '请输入公告内容', trigger: 'blur' },
    { max: 1000, message: '内容长度不能超过1000个字符', trigger: 'blur' }
  ],
  noticeType: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ],
  jumpType: [
    { required: true, message: '请选择跳转类型', trigger: 'change' }
  ],
  jumpUrl: [
    {
      validator: (_rule, value, callback) => {
        if (formData.jumpType !== 0 && !value) {
          callback(new Error('跳转类型不为"不跳转"时，跳转地址为必填'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 获取公告列表
const getNoticeList = async () => {
  loading.value = true;
  try {
    const response = await getSystemNoticeListApi({ merchantGuid: '' });
    noticeList.value = response.data.data || [];
    total.value = response.data.total || 0;
  } catch (error) {
    ElMessage.error('获取公告列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getNoticeList();
};

const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  getNoticeList();
};

// 搜索
const onSearch = () => {
  searchParams.page = 1;
  getNoticeList();
};

// 重置搜索
const onReset = () => {
  searchParams.noticeType = '';
  searchParams.status = '';
  searchParams.page = 1;
  getNoticeList();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    guid: '',
    merchantGuid: merchantGuid.value,
    noticeTitle: '',
    noticeContent: '',
    noticeType: 1,
    noticeIcon: '',
    jumpType: 0,
    jumpUrl: '',
    isTop: 1,
    status: 1,
    publishTime: '',
    expireTime: '',
  });
  formRef.value?.resetFields();
};

// 新增公告
const onAdd = () => {
  dialogVisible.value = true;
  dialogType.value = 'ADD';
  resetForm(); // 确保在新增公告时调用重置表单方法
};

// 编辑公告
const onEdit = (row: any) => {
  dialogVisible.value = true;
  dialogType.value = 'EDIT';
  Object.assign(formData, {
    guid: row.guid,
    merchantGuid: row.merchantGuid,
    noticeTitle: row.noticeTitle,
    noticeContent: row.noticeContent,
    noticeType: row.noticeType,
    noticeIcon: row.noticeIcon,
    jumpType: row.jumpType,
    jumpUrl: row.jumpUrl,
    isTop: row.isTop,
    status: row.status,
    publishTime: row.publishTime,
    expireTime: row.expireTime,
  });
};

// 删除公告
const onDelete = async (row: any) => {
  try {
    await deleteSystemNoticeListApi({ guid: row.guid });
    ElMessage.success('删除成功');
    getNoticeList();
  } catch (error) {
    console.error('删除失败:', error);
  }
};

// 提交表单
const onSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    submitData.merchantGuid = merchantGuid.value;

    if (dialogType.value === 'ADD') {
      await createSystemNoticeListApi(submitData);
      ElMessage.success('新增成功');
    } else {
      await updateSystemNoticeListApi(submitData);
      ElMessage.success('修改成功');
    }

    dialogVisible.value = false;
    getNoticeList();
  } catch (error) {
    console.error('提交失败:', error);
  }
};

// 取消
const onCancel = () => {
  dialogVisible.value = false;
  resetForm();
};

// 获取公告类型文本
const getNoticeTypeText = (type: number) => {
  const typeItem = noticeTypes.find(item => item.value === type);
  return typeItem ? typeItem.label : '';
};

// 获取状态文本
const getStatusText = (status: number) => {
  return status === 1 ? '启用' : '禁用';
};

// 获取跳转类型文本
const getJumpTypeText = (type: number) => {
  const typeItem = jumpTypes.find(item => item.value === type);
  return typeItem ? typeItem.label : '';
};

// 初始化
onMounted(() => {
  getNoticeList();
});
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <!-- 搜索栏 -->
        <div class="header-box">
          <el-form :inline="true" :model="searchParams" class="demo-form-inline">
            <el-form-item label="公告类型">
              <el-select v-model="searchParams.noticeType" placeholder="请选择公告类型" clearable>
                <el-option v-for="item in noticeTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchParams.status" placeholder="请选择状态" clearable>
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
              <el-button type="primary" @click="onAdd">新增公告</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格 -->
        <el-table :data="noticeList" border style="width: 100%">
          <el-table-column prop="sysId" label="ID" width="80" />
          <el-table-column prop="noticeTitle" label="公告标题" min-width="150" show-overflow-tooltip />
          <el-table-column prop="noticeContent" label="公告内容" min-width="200" show-overflow-tooltip />
          <el-table-column prop="noticeType" label="公告类型" width="120">
            <template #default="{ row }">
              <el-tag
                :type="row.noticeType === 1 ? 'info' : row.noticeType === 2 ? 'success' : row.noticeType === 3 ? 'warning' : 'danger'">
                {{ getNoticeTypeText(row.noticeType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="jumpType" label="跳转类型" width="120">
            <template #default="{ row }">
              {{ getJumpTypeText(row.jumpType) }}
            </template>
          </el-table-column>
          <el-table-column prop="jumpUrl" label="跳转地址" min-width="150" show-overflow-tooltip />
          <el-table-column prop="isTop" label="置顶" width="80">
            <template #default="{ row }">
              <el-tag :type="row.isTop === 1 ? 'success' : 'info'">
                {{ row.isTop === 1 ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publishTime" label="发布时间" width="160" />
          <el-table-column prop="expireTime" label="失效时间" width="160" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="onEdit(row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="确定删除这条公告吗？"
                @confirm="onDelete(row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>

    <!-- 新增/编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'ADD' ? '新增公告' : '编辑公告'" width="600px" @close="onCancel">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-form-item label="公告标题" prop="noticeTitle">
          <el-input v-model="formData.noticeTitle" placeholder="请输入公告标题" maxlength="100" show-word-limit />
        </el-form-item>

        <el-form-item label="公告内容" prop="noticeContent">
          <el-input v-model="formData.noticeContent" type="textarea" :rows="4" placeholder="请输入公告内容" maxlength="1000"
            show-word-limit />
        </el-form-item>

        <el-form-item label="公告类型" prop="noticeType">
          <el-select v-model="formData.noticeType" placeholder="请选择公告类型">
            <el-option v-for="item in noticeTypes.filter(type => type.value !== '')" :key="item.value"
              :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="公告图标" prop="noticeIcon">
          <el-input v-model="formData.noticeIcon" placeholder="请输入图标名称或留空使用默认图标" />
        </el-form-item> -->

        <el-form-item label="跳转类型" prop="jumpType">
          <el-select v-model="formData.jumpType" placeholder="请选择跳转类型">
            <el-option v-for="item in jumpTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="跳转地址" prop="jumpUrl" v-if="formData.jumpType !== 0">
          <el-input v-model="formData.jumpUrl" placeholder="请输入跳转地址" />
        </el-form-item>

        <el-form-item label="是否置顶">
          <el-radio-group v-model="formData.isTop">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="发布时间">
          <el-date-picker v-model="formData.publishTime" type="datetime" placeholder="选择发布时间"
            format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>

        <el-form-item label="失效时间">
          <el-date-picker v-model="formData.expireTime" type="datetime" placeholder="选择失效时间"
            format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.wrapper {
  height: 100vh;

  .header-box {
    background: #f5f7fa;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .demo-form-inline {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background-color: #f5f7fa;

    th {
      background-color: #f5f7fa !important;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 标签样式优化
:deep(.el-tag) {
  border-radius: 4px;
  font-size: 12px;
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  .el-input__inner,
  .el-textarea__inner {
    border-radius: 4px;
  }

  .el-select {
    width: 100%;
  }
}

// 按钮样式优化
:deep(.el-button) {
  border-radius: 4px;
  font-weight: 500;

  &.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;

    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }

  &.el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;

    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}
</style>
