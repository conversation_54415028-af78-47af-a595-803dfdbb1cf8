import{d as le,a as w,r as p,c as b,b as e,w as n,h as o,A as ae,at as te,e as oe,C as ne,au as se,o as c,f as k,i as v,G as re,H as de,I as ie,T as ue,ah as pe,ai as me,E as m,J as _e,av as ce,aw as ge,ax as fe,ay as we,p as be,l as ve,q as Ve,s as he,N as ke,v as ye,y as xe,x as Ee,Y as Ce,m as Re,n as Ue,k as qe,O as Ae,P as Le,a3 as Ie,az as Fe,_ as ze}from"./index-5837f9dc.js";/* empty css                  *//* empty css                   *//* empty css                        *//* empty css                 *//* empty css                  *//* empty css                    */import{_ as Ge,a as Te}from"./plus-acd24b23.js";/* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                        *//* empty css                     */const De={class:"header-box"},Be={class:"upload-img-box"},Ne=["src"],je={key:1,class:"upload-btn"},Me={class:"operate-box"},Pe=le({__name:"AiRoleListView",setup(Se){const y=w({merchantGuid:"",pageSize:100,page:1}),x=p([]),E=async()=>{y.merchantGuid=a.merchantGuid;let s=await Fe(y);x.value=s.data.data};let C=p([]);const V=w({limit:10,page:1,merchantGuid:""}),R=p(0),g=async()=>{let s=await te(V);R.value=s.data.total,C.value=s.data.data},L=async s=>{V.page=s,g()};let u=p(!1),a=w({id:0,merchantGuid:"",name:"",standing:"",signature:"",avatar:"",chat_expense:0,add_expense:0,sort:0,rule:"",welcome:"",build_knowledge_guids:[],knowledge_use_params:"针对用户的问题，我为你提供了知识库内容，知识库内容中的score代表了匹配度，满分是0.99，分数大于0.3就必须高度重视'，越高越匹配，content代表了知识库的内容。请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是："});const I=()=>{u.value=!0,f.value="add",E()},F=s=>{a=Object.assign(a,s),a.merchantGuid=s.merchant_guid,E(),u.value=!0,a.id=s.id,f.value="edit"};let f=p("");const z=p(),G=s=>{let l=["image/jpg","image/png","image/jpeg"];if(l.includes(s.type)){if(s.size/1024/1024>2)return m.error("图片必须小于2M"),!1}else return m.warning("当前图片仅支持格式为："+l.join(" ，")),!1;return!0},T=async s=>{const l=new FormData;l.append("img",s.file);let i=await _e(l);a.avatar=i.data},D=()=>{a.avatar=""},U=p(),B=w({name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],standing:[{required:!0,message:"请输入角色身份",trigger:"blur"}],signature:[{required:!0,message:"请输入个性签名",trigger:"blur"}],avatar:[{required:!0,message:"请选上传头像",trigger:"blur"}],rule:[{required:!0,message:"请选输入身份规则说明",trigger:"blur"}],welcome:[{required:!0,message:"请选输入欢迎语",trigger:"blur"}]}),N=async s=>{s.validate(async l=>{if(l)try{f.value==="add"?(await ce(a),m.success("新增成功")):f.value==="edit"&&(await ge(a),m.success("修改成功")),fe(()=>{s.resetFields(),u.value=!1}),g()}catch(i){throw m.error(i),new Error(i)}})},j=async s=>{let l={id:s.id};await we(l),m.success("删除成功"),g()};return g(),(s,l)=>{const i=be,r=ve,q=Ve,d=he,M=ke,P=ye,S=xe,O=Ee,K=Ce,$=oe,H=Re,J=Ue,_=qe,Y=Ge,A=Ae,Q=Te,W=Le,h=Ie,X=se("v-md-editor"),Z=ne;return c(),b("div",null,[e($,{class:"wrapper"},{default:n(()=>[e(S,null,{default:n(()=>[k("div",De,[e(q,{inline:!0,class:"demo-form-inline"},{default:n(()=>[e(r,null,{default:n(()=>[e(i,{type:"primary",onClick:I},{default:n(()=>[v("新增Ai好友")]),_:1})]),_:1})]),_:1})]),e(P,{data:o(C),border:"",style:{width:"100%"}},{default:n(()=>[e(d,{prop:"id",label:"角色id",width:"80"}),e(d,{prop:"name",label:"名称",width:"150"}),e(d,{prop:"standing",label:"身份",width:"150"}),e(d,{prop:"signature",label:"个性签名",width:"180"}),e(d,{prop:"avatar","show-overflow-tooltip":!0,label:"头像",width:"100"},{default:n(t=>[e(M,{src:t.row.avatar,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src"])]),_:1}),e(d,{prop:"chat_expense",label:"聊天一次消耗点数",width:"120"}),e(d,{prop:"add_expense",label:"添加好友消耗点数",width:"120"}),e(d,{prop:"rule",label:"身份规则说明","show-overflow-tooltip":!0,width:"180"}),e(d,{prop:"welcome",label:"欢迎语",width:"180"}),e(d,{label:"操作"},{default:n(t=>[e(i,{size:"small",type:"primary",onClick:ee=>F(t.row)},{default:n(()=>[v("角色编辑")]),_:2},1032,["onClick"]),e(i,{size:"small",type:"warning",onClick:ee=>j(t.row)},{default:n(()=>[v("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e(K,null,{default:n(()=>[e(O,{background:"",layout:"prev,pager, next",total:o(R),"current-page":o(V).page,onCurrentChange:L},null,8,["total","current-page"])]),_:1})]),_:1}),e(Z,{modelValue:o(u),"onUpdate:modelValue":l[11]||(l[11]=t=>ae(u)?u.value=t:u=t),title:"新增/编辑 角色",width:"1000px"},{default:n(()=>[e(q,{ref_key:"addForm",ref:U,model:o(a),class:"demo-form-inline","label-width":"100px",rules:o(B)},{default:n(()=>[e(r,{label:"选择知识库",prop:"build_knowledge_guids"},{default:n(()=>[e(J,{modelValue:o(a).build_knowledge_guids,"onUpdate:modelValue":l[0]||(l[0]=t=>o(a).build_knowledge_guids=t),placeholder:"请选择",multiple:"","multiple-limit":1},{default:n(()=>[(c(!0),b(re,null,de(o(x),t=>(c(),ie(H,{label:t.knowledgeTitle,value:t.guid,key:t.guid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"知识库参数",prop:"knowledge_use_params"},{default:n(()=>[e(_,{modelValue:o(a).knowledge_use_params,"onUpdate:modelValue":l[1]||(l[1]=t=>o(a).knowledge_use_params=t)},null,8,["modelValue"])]),_:1}),e(r,{label:"角色名称",prop:"name"},{default:n(()=>[e(_,{modelValue:o(a).name,"onUpdate:modelValue":l[2]||(l[2]=t=>o(a).name=t),placeholder:"角色名称"},null,8,["modelValue"])]),_:1}),e(r,{label:"角色身份",prop:"standing"},{default:n(()=>[e(_,{modelValue:o(a).standing,"onUpdate:modelValue":l[3]||(l[3]=t=>o(a).standing=t),placeholder:"角色身份"},null,8,["modelValue"])]),_:1}),e(r,{label:"个性签名",prop:"signature"},{default:n(()=>[e(_,{modelValue:o(a).signature,"onUpdate:modelValue":l[4]||(l[4]=t=>o(a).signature=t),placeholder:"个性签名"},null,8,["modelValue"])]),_:1}),e(r,{label:"头像",prop:"avatar"},{default:n(()=>[e(W,{ref_key:"uploadImgRef",ref:z,class:"avatar-uploader","before-upload":G,"show-file-list":!1,"http-request":T},{default:n(()=>[k("div",Be,[o(a).avatar?(c(),b("img",{key:0,src:o(a).avatar,class:"preview-img"},null,8,Ne)):(c(),b("div",je,[e(A,{size:"30",color:"#cdd0d6"},{default:n(()=>[e(Y)]),_:1})])),ue(k("div",Me,[e(A,{size:"22",color:"#ffffff",onClick:me(D,["stop"])},{default:n(()=>[e(Q)]),_:1},8,["onClick"])],512),[[pe,o(a).avatar]])])]),_:1},512)]),_:1}),e(r,{label:"聊天消耗点数",prop:"chat_expense"},{default:n(()=>[e(h,{modelValue:o(a).chat_expense,"onUpdate:modelValue":l[5]||(l[5]=t=>o(a).chat_expense=t),min:0},null,8,["modelValue"])]),_:1}),e(r,{label:"添加好友消耗点数",prop:"add_expense"},{default:n(()=>[e(h,{modelValue:o(a).add_expense,"onUpdate:modelValue":l[6]||(l[6]=t=>o(a).add_expense=t),min:0},null,8,["modelValue"])]),_:1}),e(r,{label:"排序",prop:"sort"},{default:n(()=>[e(h,{modelValue:o(a).sort,"onUpdate:modelValue":l[7]||(l[7]=t=>o(a).sort=t),min:0},null,8,["modelValue"])]),_:1}),e(r,{label:"提示语",prop:"rule"},{default:n(()=>[e(X,{modelValue:o(a).rule,"onUpdate:modelValue":l[8]||(l[8]=t=>o(a).rule=t),height:"400px"},null,8,["modelValue"])]),_:1}),e(r,{label:"欢迎语",prop:"welcome"},{default:n(()=>[e(_,{modelValue:o(a).welcome,"onUpdate:modelValue":l[9]||(l[9]=t=>o(a).welcome=t),placeholder:"欢迎语"},null,8,["modelValue"])]),_:1}),e(r,null,{default:n(()=>[e(i,{type:"primary",onClick:l[10]||(l[10]=t=>N(o(U)))},{default:n(()=>[v("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const ol=ze(Pe,[["__scopeId","data-v-0d1ce5ec"]]);export{ol as default};
