import{d as fe,a5 as ge,bd as P,r as v,a6 as be,a as C,be as ye,c as u,b as e,w as t,h as a,bf as he,e as ve,C as Ce,o as p,T as F,I as we,f as _,i as w,G as I,H as V,U as xe,ah as Ee,ai as Ie,E as f,J as Ve,bg as De,bh as ke,bi as Ue,ax as Te,p as Ne,l as Se,q as Pe,s as Fe,N as Le,af as Ae,v as Ge,y as ze,x as Re,Y as je,bj as qe,bk as Be,k as Me,m as Oe,n as He,a3 as $e,O as Je,P as Ke,Z as Ye,_ as Ze}from"./index-2d10794a.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                    */import{_ as Qe,a as We}from"./plus-25c99156.js";/* empty css                        *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                      *//* empty css                   *//* empty css                        *//* empty css                     *//* empty css              */import{T as L,E as A}from"./index.esm-fb464a93.js";const Xe={class:"hearder-box"},eo=["innerHTML"],oo={style:{border:"1px solid #ccc"}},ao={style:{border:"1px solid #ccc"}},to={class:"upload-img-box"},lo=["src"],no={key:1,class:"upload-btn"},ro=["onClick"],so=fe({__name:"vipListView",setup(io){const G=ge(),x=P(),E=P(),D={excludeKeys:["group-image","insertVideo","group-video"]},z=r=>{x.value=r},R=r=>{E.value=r},j=v(0),m=v(!1);let k=be(()=>G.getTenantInfo.guid);const g=C({merchantGuid:"",pageSize:10,page:1});let U=v([]);const y=async()=>{g.merchantGuid=k.value,m.value=!0;let r=await he(g);m.value=!1,U.value=r.data},q=async r=>{g.page=r,y()},i=C({dialogtype:"ADD",isShow:!1}),B=C({cardName:[{required:!0,message:"请输入会员卡名称",trigger:"change"}],bgImg:[{required:!0,message:"上传背景",trigger:"change"}]}),T=[{type:"chat",typeDesc:"AI聊天",isEnable:!0},{type:"img",typeDesc:"AI绘画",isEnable:!0},{type:"shuziren",typeDesc:"AI数字人",isEnable:!0}],M=[{name:"月卡",value:"1"},{name:"季卡",value:"2"},{name:"年卡",value:"3"}],N=v();let n=C({merchantGuid:"",cardName:"",cardType:"1",cardDesc:"",cardPrice:0,cardNotice:"",cardUseConfig:[],cardUseConfigIndex:[],bgImg:""});const O=()=>{i.isShow=!0,i.dialogtype="ADD"},H=r=>{let o=["image/jpg","image/png","image/jpeg"];if(!o.includes(r.type))return f.warning("当前图片仅支持格式为："+o.join(" ，")),!1},$=async r=>{const o=new FormData;o.append("img",r.file);let s=await Ve(o);n.bgImg=s.data},J=()=>{n.bgImg=""},K=r=>{n=Object.assign(n,r),n.cardUseConfigIndex=[],r.cardUseConfig.forEach(o=>{n.cardUseConfigIndex.push(o.typeDesc)}),n.cardPrice=parseFloat(r.cardPrice),i.isShow=!0,i.dialogtype="EDIT"},Y=async r=>{try{let o=r.guid;m.value=!0,await De({guid:o}),m.value=!1,y(),f.success("删除成功")}catch(o){m.value=!1,f.success(o)}},Z=async r=>{r.validate(async o=>{if(o)try{n.merchantGuid=k.value,i.dialogtype==="ADD"?(n.cardUseConfigIndex.forEach(s=>{let d=T.find(h=>h.typeDesc==s);n.cardUseConfig.push(d)}),await ke(n),f.success("新增成功")):i.dialogtype==="EDIT"&&(await Ue(n),f.success("修改成功")),Te(()=>{r.resetFields(),i.isShow=!1}),y()}catch(s){throw f.error(s),new Error(s)}})};return ye(()=>{const r=x.value,o=E.value;o!=null&&(o.destroy(),r!=null&&r.destroy())}),y(),(r,o)=>{const s=Ne,d=Se,h=Pe,c=Fe,Q=Le,W=Ae,X=Ge,ee=ze,oe=Re,ae=je,te=ve,le=qe,ne=Be,re=Me,se=Oe,de=He,ie=$e,ce=Qe,S=Je,pe=We,ue=Ke,me=Ce,_e=Ye;return p(),u("div",null,[e(te,{class:"wrapper"},{default:t(()=>[F((p(),we(ee,null,{default:t(()=>[_("div",Xe,[e(h,{inline:!0,model:a(g),class:"demo-form-inline"},{default:t(()=>[e(d,null,{default:t(()=>[e(s,{type:"primary",onClick:O},{default:t(()=>[w("新增")]),_:1})]),_:1})]),_:1},8,["model"])]),e(X,{data:a(U),border:"",style:{width:"100%"}},{default:t(()=>[e(c,{prop:"sysId",label:"CardId",width:"80"}),e(c,{prop:"cardName",label:"会员卡名称",width:"180"}),e(c,{prop:"cardPrice",label:"会员卡价格",width:"120"}),e(c,{prop:"bgImg",label:"会员卡背景",width:"120"},{default:t(l=>[e(Q,{src:l.row.bgImg,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e(c,{prop:"cardType",label:"会员卡类型",width:"120"}),e(c,{prop:"cardUseConfig",label:"会员卡配置"},{default:t(l=>[(p(!0),u(I,null,V(l.row.cardUseConfig,b=>(p(),u("span",null,xe(b.typeDesc)+"  ",1))),256))]),_:1}),e(c,{prop:"cardNotice",label:"会员卡说明"},{default:t(l=>[_("div",{innerHTML:l.row.cardNotice},null,8,eo)]),_:1}),e(c,{prop:"cardDesc",label:"会员卡描述"}),e(c,{label:"操作"},{default:t(l=>[e(s,{size:"small",type:"primary",onClick:b=>K(l.row)},{default:t(()=>[w("编辑")]),_:2},1032,["onClick"]),e(W,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该会员卡?",onConfirm:b=>Y(l.row)},{reference:t(()=>[e(s,{size:"small",type:"danger"},{default:t(()=>[w("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])]),_:1})),[[_e,a(m)]]),e(ae,null,{default:t(()=>[e(oe,{background:"",layout:"prev,pager, next",total:a(j),"current-page":a(g).page,onCurrentChange:q},null,8,["total","current-page"])]),_:1})]),_:1}),e(me,{modelValue:a(i).isShow,"onUpdate:modelValue":o[7]||(o[7]=l=>a(i).isShow=l),title:"创建修改会员卡",width:"1000px"},{default:t(()=>[e(h,{ref_key:"addForm",ref:N,model:a(n),class:"demo-form-inline","label-width":"120px",rules:a(B)},{default:t(()=>[e(d,{label:"会员卡可用权益"},{default:t(()=>[e(ne,{modelValue:a(n).cardUseConfigIndex,"onUpdate:modelValue":o[0]||(o[0]=l=>a(n).cardUseConfigIndex=l)},{default:t(()=>[(p(),u(I,null,V(T,(l,b)=>e(le,{value:b,label:l.typeDesc},null,8,["value","label"])),64))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"会员卡名称",prop:"cardName"},{default:t(()=>[e(re,{modelValue:a(n).cardName,"onUpdate:modelValue":o[1]||(o[1]=l=>a(n).cardName=l),placeholder:"名称"},null,8,["modelValue"])]),_:1}),e(d,{label:"会员卡类型",prop:"cardType"},{default:t(()=>[e(de,{modelValue:a(n).cardType,"onUpdate:modelValue":o[2]||(o[2]=l=>a(n).cardType=l),class:"m-2",placeholder:"Select",size:"large",style:{width:"240px"}},{default:t(()=>[(p(),u(I,null,V(M,l=>e(se,{key:l.value,label:l.name,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"会员卡描述",prop:"cardDesc"},{default:t(()=>[_("div",oo,[e(a(L),{style:{"border-bottom":"1px solid #ccc"},defaultConfig:D,editor:a(x),mode:"default"},null,8,["editor"]),e(a(A),{style:{height:"300px","overflow-y":"hidden"},modelValue:a(n).cardDesc,"onUpdate:modelValue":o[3]||(o[3]=l=>a(n).cardDesc=l),mode:"default",onOnCreated:z},null,8,["modelValue"])])]),_:1}),e(d,{label:"会员卡说明",prop:"cardNotice"},{default:t(()=>[_("div",ao,[e(a(L),{style:{"border-bottom":"1px solid #ccc"},defaultConfig:D,editor:a(E),mode:"default"},null,8,["editor"]),e(a(A),{style:{height:"300px","overflow-y":"hidden"},modelValue:a(n).cardNotice,"onUpdate:modelValue":o[4]||(o[4]=l=>a(n).cardNotice=l),mode:"default",onOnCreated:R},null,8,["modelValue"])])]),_:1}),e(d,{label:"会员卡价格",prop:"cardPrice"},{default:t(()=>[e(ie,{modelValue:a(n).cardPrice,"onUpdate:modelValue":o[5]||(o[5]=l=>a(n).cardPrice=l),precision:2,step:.01},null,8,["modelValue","step"])]),_:1}),e(d,{label:"会员卡背景",prop:"bgImg"},{default:t(()=>[e(ue,{ref:"uploadImgRef",class:"avatar-uploader","before-upload":H,"show-file-list":!1,"http-request":$},{default:t(()=>[_("div",to,[a(n).bgImg?(p(),u("img",{key:0,src:a(n).bgImg,class:"preview-img"},null,8,lo)):(p(),u("div",no,[e(S,{size:"30",color:"#cdd0d6"},{default:t(()=>[e(ce)]),_:1})])),F(_("div",{class:"operate-box",onClick:Ie(J,["stop"])},[e(S,{size:"22",color:"#ffffff"},{default:t(()=>[e(pe)]),_:1})],8,ro),[[Ee,a(n).bgImg]])])]),_:1},512)]),_:1}),e(d,null,{default:t(()=>[e(s,{type:"primary",onClick:o[6]||(o[6]=l=>Z(a(N)))},{default:t(()=>[w("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const ko=Ze(so,[["__scopeId","data-v-6702c1cf"]]);export{ko as default};
