import{d as z,r as c,a as C,z as R,c as L,b as e,w as a,h as s,A as M,an as $,e as S,C as Z,D as j,o as y,T as H,I as J,f as K,i as _,ao as O,E as g,ap as P,aq as Q,p as W,s as X,v as Y,y as ee,k as oe,l as ae,a3 as te,q as le,Z as se,_ as de}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                        *//* empty css                 *//* empty css                        *//* empty css               */const ne={class:"header-box"},re=z({__name:"ChatGoodsView",setup(ue){const h=j(),f=c(!1),v=c();let n=c(!1),p=c("");const w=C({merchantGuid:""}),o=C({merchantGuid:"",goodsGuid:"",goodsName:"",goodsDesc:"",chatCount:1,price:""});let V=c([]);const E=C({goodsName:[{required:!0,message:"请输入商品名",trigger:"blur"}],goodsDesc:[{required:!0,message:"请输入描述",trigger:"blur"}],chatCount:[{required:!0,message:"输入点数",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"}]}),m=async()=>{f.value=!0,w.merchantGuid=h.query.guid;let d=await $(w);f.value=!1,V.value=d.data},D=()=>{n.value=!0,p.value="add"},G=d=>{o.goodsGuid=d.guid,o.goodsName=d.goodsName,o.goodsDesc=d.goodsDesc,o.chatCount=d.chatCount,o.price=d.price,n.value=!0,p.value="edit"},N=async d=>{await O({goodsGuid:d.guid}),g.success("删除成功"),m()},k=d=>{o.merchantGuid=h.query.guid,d.validate(async t=>{if(t)try{p.value==="add"?(await P(o),g.success("新增成功")):p.value==="edit"&&(await Q(o),g.success("修改成功")),d.resetFields(),n.value=!1,m()}catch(r){throw g.error(r),new Error(r)}})};return R(()=>{m()}),m(),(d,t)=>{const r=W,u=X,q=Y,x=ee,A=S,b=oe,i=ae,F=te,I=le,T=Z,B=se;return y(),L("div",null,[e(A,{class:"wrapper"},{default:a(()=>[H((y(),J(x,null,{default:a(()=>[K("div",ne,[e(r,{type:"primary",onClick:D},{default:a(()=>[_("新增商品")]),_:1})]),e(q,{data:s(V),border:"",style:{width:"100%"}},{default:a(()=>[e(u,{prop:"sysId",label:"商品Id",width:"80"}),e(u,{prop:"goodsName",label:"商品昵称",width:"180"}),e(u,{prop:"goodsDesc",label:"商品描述"}),e(u,{prop:"chatCount",label:"商品点数",width:"100"}),e(u,{prop:"price",label:"商品价格",width:"100"}),e(u,{label:"操作"},{default:a(l=>[e(r,{size:"small",type:"primary",onClick:U=>G(l.row)},{default:a(()=>[_("编辑")]),_:2},1032,["onClick"]),e(r,{size:"small",type:"danger",onClick:U=>N(l.row)},{default:a(()=>[_("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[B,s(f)]])]),_:1}),e(T,{modelValue:s(n),"onUpdate:modelValue":t[5]||(t[5]=l=>M(n)?n.value=l:n=l),title:"修改/新增 商品",width:"600px"},{default:a(()=>[e(I,{ref_key:"addForm",ref:v,model:s(o),class:"demo-form-inline","label-width":"100px",rules:s(E)},{default:a(()=>[e(i,{label:"商品名称",prop:"goodsName"},{default:a(()=>[e(b,{modelValue:s(o).goodsName,"onUpdate:modelValue":t[0]||(t[0]=l=>s(o).goodsName=l),placeholder:"商品名称"},null,8,["modelValue"])]),_:1}),e(i,{label:"商品描述",prop:"goodsDesc"},{default:a(()=>[e(b,{modelValue:s(o).goodsDesc,"onUpdate:modelValue":t[1]||(t[1]=l=>s(o).goodsDesc=l),placeholder:"商品描述"},null,8,["modelValue"])]),_:1}),e(i,{label:"商品点数",prop:"chatCount"},{default:a(()=>[e(F,{modelValue:s(o).chatCount,"onUpdate:modelValue":t[2]||(t[2]=l=>s(o).chatCount=l),min:1},null,8,["modelValue"])]),_:1}),e(i,{label:"商品价格",prop:"price"},{default:a(()=>[e(b,{modelValue:s(o).price,"onUpdate:modelValue":t[3]||(t[3]=l=>s(o).price=l),type:"n",placeholder:"商品价格"},null,8,["modelValue"])]),_:1}),e(i,null,{default:a(()=>[e(r,{type:"primary",onClick:t[4]||(t[4]=l=>k(s(v)))},{default:a(()=>[_("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const he=de(re,[["__scopeId","data-v-d723668d"]]);export{he as default};
