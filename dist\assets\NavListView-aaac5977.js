import{d as X,r as g,a as y,c as s,b as t,w as r,h as l,aQ as ee,e as le,C as te,o as n,f as p,i as v,T as m,I as S,G as I,H as E,ah as w,aC as D,aR as x,aS as ae,E as b,aT as oe,aU as re,ax as ie,p as ne,l as de,q as se,s as ue,af as ce,v as pe,y as _e,x as ge,Y as me,m as be,n as fe,k as he,Z as ye,_ as ve}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                   *//* empty css                     */const we={class:"header-box"},xe={class:"text-color-box"},ke={class:"white"},Te={class:"dark"},Ve=X({__name:"NavListView",setup(Ie){const N=g(!1),k=y({merchantGuid:"",pageSize:10,page:1}),L=g([]),C=g(0),f=async()=>{let i=await ee(k);L.value=i.data.data,C.value=i.data.total},F=async i=>{k.page=i,f()};f();const d=y({dialogtype:"ADD",isShow:!1}),G=[{label:"H5",value:"H5"},{label:"小程序",value:"MINI"},{label:"外部小程序",value:"OUT_MINI"}],U=[{label:"深色白字",value:"w1",color:"#fff",bg:"w"},{label:"深色荧字",value:"w2",color:"#acff20",bg:"w"},{label:"深色紫字",value:"w3",color:"#1a61de",bg:"w"},{label:"深色彩字",value:"wcs",color:"linear-gradient(79deg, #002bff 0%, #ff02db 0%, #0065ff 41%, #00ff71 100%)",bg:"w"},{label:"浅色白字",value:"d1",color:"#fff",bg:"d"},{label:"浅色粉字",value:"d2",color:"#ff00db",bg:"d"},{label:"浅色橙字",value:"d3",color:"#ff0000",bg:"d"},{label:"浅色彩字",value:"dcs",color:"linear-gradient(265deg, rgb(213 3 255) 0%, rgb(255 80 0) 0%, rgb(255 172 0) 41%, rgb(10 255 0) 100%)",bg:"d"}];let M=g([{path:"pages/nav/nav",title:"AI智能体"},{path:"pages/index/index",title:"小艺AI"},{path:"pages/digit/digit",title:"数字人中心"},{path:"pages/digit/card_qrcode",title:"数字客服二维码"},{path:"pages/my/my",title:"我的"},{path:"pages/exchange/exchange",title:"兑换中心"},{path:"pages/digit/photo",title:"专属头像"},{path:"pages/my/user-info",title:"个人中心"},{path:"pages/my/user-qrcode",title:"商务合作"},{path:"pages/login/login",title:"登录"},{path:"pages/set-tip/set-tip",title:"数字人提示语"},{path:"pages/my/question",title:"常见问题"},{path:"pages/tool-house/tool",title:"智能体"},{path:"pages/tool-house/tool_msg",title:"智能体聊天页"},{path:"page_draw/draw_index/index",title:"AI绘画"},{path:"page_draw/success/draw_succes",title:"绘画详情"},{path:"page_draw/poster_index/index",title:"笨猫相机"},{path:"page_draw/poster_success/index",title:"笨猫详情"},{path:"page_digit/digit_index/index",title:"AI数字人"},{path:"page_digit/success/digit_success",title:"数字详情"},{path:"page_channel/channel_index/index",title:"AI市场"},{path:"page_channel/tool_msg/tool_msg",title:"市场聊天页"},{path:"page_user/user_index/index",title:"我的作品"},{path:"page_card/card_data/card_data",title:"数字客服数据"},{path:"page_card/user_list/user_list",title:"访问用户列表"},{path:"page_card/card_center/card_center",title:"我的数字客服"},{path:"page_card/card_list/card_list",title:"我的客服列表"},{path:"page_card/card_video_list/card_video_list",title:"我的客服视频"},{path:"page_card/card_knowledge_lib/card_knowledge_lib",title:"客服知识库"},{path:"page_card/card_knowledge_detail/card_knowledge_detail",title:"知识库管理"},{path:"page_card/card_create/card_create",title:"名片设置"},{path:"page_card/card_question/card_question",title:"基础提问库"},{path:"page_card/card_prompt/card_prompt",title:"一对一咨询"},{path:"page_card/card_msg/card_msg",title:"客服聊天"}]),e=y({merchantGuid:"",guid:"",urlType:"H5",urlLink:"",urlTitle:"",colorType:"",coloritem:{label:"默认",value:"d1",color:"#fff",bg:"d"}});const A=g(),z=y({merchantGuid:[{required:!0,message:"请选择所属商家",trigger:"change"}],urlType:[{required:!0,message:"请选择跳转类型",trigger:"change"}],urlLink:[{required:!0,message:"请输入跳转地址",trigger:"change"}],urlTitle:[{required:!0,message:"请输入跳转标题",trigger:"blur"}],coloritem:[{required:!0,message:"请选择前端配色方案",trigger:"change"}]}),B=()=>{d.isShow=!0,d.dialogtype="ADD"},H=i=>{e.merchantGuid=i.merchantGuid,e.guid=i.guid,e.urlType=i.urlType,e.urlLink=i.urlLink,e.urlTitle=i.urlTitle,U.forEach(o=>{i.colorType===o.value&&(e.coloritem=o)}),d.isShow=!0,d.dialogtype="EDIT"},P=async i=>{try{let o=i.guid;await ae({guid:o}),f(),b.success("删除成功")}catch(o){b.success(o)}},O=async i=>{i.validate(async o=>{if(o)try{e.colorType=e.coloritem.value,d.dialogtype==="ADD"?(await oe(e),b.success("新增成功")):d.dialogtype==="EDIT"&&(await re(e),b.success("修改成功")),ie(()=>{i.resetFields(),d.isShow=!1}),f()}catch(u){throw b.error(u),new Error(u)}})};return(i,o)=>{const u=ne,c=de,q=se,_=ue,R=ce,$=pe,Q=_e,Y=ge,Z=me,j=le,T=be,V=fe,h=he,J=te,K=ye;return n(),s("div",null,[t(j,{class:"wrapper"},{default:r(()=>[t(Q,null,{default:r(()=>[p("div",we,[t(q,{inline:!0,class:"demo-form-inline"},{default:r(()=>[t(c,null,{default:r(()=>[t(u,{type:"primary",onClick:B},{default:r(()=>[v("新增配置")]),_:1})]),_:1})]),_:1})]),m((n(),S($,{data:l(L),border:"",style:{width:"100%"}},{default:r(()=>[t(_,{prop:"sysId",label:"id",width:"80"}),t(_,{prop:"urlTitle",label:"跳转标题"}),t(_,{prop:"urlType",label:"跳转类型"}),t(_,{prop:"urlLink",label:"跳转地址"}),t(_,{label:"操作"},{default:r(a=>[t(u,{size:"small",type:"primary",onClick:W=>H(a.row)},{default:r(()=>[v("编辑")]),_:2},1032,["onClick"]),t(R,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该配置?",onConfirm:W=>P(a.row)},{reference:r(()=>[t(u,{size:"small",type:"danger"},{default:r(()=>[v("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[K,l(N)]])]),_:1}),t(Z,null,{default:r(()=>[t(Y,{background:"",layout:"prev,pager, next",total:l(C),"current-page":l(k).page,onCurrentChange:F},null,8,["total","current-page"])]),_:1})]),_:1}),t(J,{modelValue:l(d).isShow,"onUpdate:modelValue":o[8]||(o[8]=a=>l(d).isShow=a),title:"创建/修改 链接配置",width:"600",draggable:""},{default:r(()=>[t(q,{ref_key:"addForm",ref:A,model:l(e),class:"demo-form-inline",rules:l(z),"label-width":"100px"},{default:r(()=>[t(c,{label:"跳转类型",prop:"urlType"},{default:r(()=>[t(V,{modelValue:l(e).urlType,"onUpdate:modelValue":o[0]||(o[0]=a=>l(e).urlType=a),placeholder:"请选择跳转类型"},{default:r(()=>[(n(),s(I,null,E(G,a=>t(T,{label:a.label,value:a.value,key:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"分享路径",prop:"urlLink"},{default:r(()=>[m(t(h,{modelValue:l(e).urlLink,"onUpdate:modelValue":o[2]||(o[2]=a=>l(e).urlLink=a),placeholder:"pages/nav/nav"},{append:r(()=>[t(V,{modelValue:l(e).urlLink,"onUpdate:modelValue":o[1]||(o[1]=a=>l(e).urlLink=a),placeholder:"选择url",style:{width:"115px"}},{default:r(()=>[(n(!0),s(I,null,E(l(M),a=>(n(),S(T,{key:a.path,label:a.title,value:a.path},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),[[w,l(e).urlType==="MINI"]]),m(t(h,{modelValue:l(e).urlLink,"onUpdate:modelValue":o[3]||(o[3]=a=>l(e).urlLink=a),placeholder:"请输入url地址"},null,8,["modelValue"]),[[w,l(e).urlType==="H5"]]),m(t(h,{modelValue:l(e).urlLink,"onUpdate:modelValue":o[4]||(o[4]=a=>l(e).urlLink=a),placeholder:"请输入小程序APPID"},null,8,["modelValue"]),[[w,l(e).urlType==="OUT_MINI"]])]),_:1}),t(c,{label:"分享标题",prop:"urlTitle"},{default:r(()=>[t(h,{modelValue:l(e).urlTitle,"onUpdate:modelValue":o[5]||(o[5]=a=>l(e).urlTitle=a),placeholder:"请输入跳转标题"},null,8,["modelValue"])]),_:1}),t(c,{label:"配色方案",prop:"coloritem"},{default:r(()=>[t(V,{modelValue:l(e).coloritem,"onUpdate:modelValue":o[6]||(o[6]=a=>l(e).coloritem=a),placeholder:"请选择前端配色方案"},{default:r(()=>[(n(),s(I,null,E(U,a=>t(T,{label:a.label,value:a,key:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"样式展示"},{default:r(()=>[m(p("div",xe,[p("div",ke,[p("div",{class:D(["item",{"bg-white":l(e).coloritem.bg=="w"},{"bg-dark":l(e).coloritem.bg=="d"}])},[l(e).coloritem.value=="dcs"||l(e).coloritem.value=="wcs"?(n(),s("text",{key:0,class:"color-text",style:x({backgroundImage:l(e).coloritem.color})},"测试文字1",4)):(n(),s("text",{key:1,style:x({color:l(e).coloritem.color})},"测试文字1",4))],2)]),p("div",Te,[p("div",{class:D(["item",{"bg-white":l(e).coloritem.bg=="w"},{"bg-dark":l(e).coloritem.bg=="d"}])},[l(e).coloritem.value=="dcs"||l(e).coloritem.value=="wcs"?(n(),s("text",{key:0,class:"color-text",style:x({backgroundImage:l(e).coloritem.color})},"测试文字",4)):(n(),s("text",{key:1,style:x({color:l(e).coloritem.color})},"测试文字",4))],2)])],512),[[w,l(e).coloritem]])]),_:1}),t(c,null,{default:r(()=>[t(u,{type:"primary",onClick:o[7]||(o[7]=a=>O(l(A)))},{default:r(()=>[v("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Me=ve(Ve,[["__scopeId","data-v-2bb65545"]]);export{Me as default};
