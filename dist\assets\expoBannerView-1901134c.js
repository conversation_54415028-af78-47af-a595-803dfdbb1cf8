import{d as xe,r as m,a as V,by as Ce,c as i,b as e,w as t,h as l,A as Z,D as je,bJ as Ee,bF as qe,e as De,C as Fe,o as r,T as B,I as p,f as d,G as j,H as E,aC as ze,U as Ie,i as f,F as c,ah as X,ai as ee,bK as Ue,E as _,J as Le,aM as Me,ax as R,bL as Ge,bM as Ae,bN as Be,l as Re,p as Se,q as Ne,s as Pe,N as $e,af as Oe,v as He,y as Je,x as Qe,Y as Ke,m as We,n as Ye,k as Ze,P as Xe,O as ea,a3 as aa,Z as ta,bb as la,bc as oa,_ as na}from"./index-5837f9dc.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                        *//* empty css                 */import{_ as sa,a as ra}from"./plus-acd24b23.js";/* empty css                  *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                   *//* empty css                        *//* empty css                     *//* empty css              */const S=q=>(la("data-v-21cdb017"),q=q(),oa(),q),ua={class:"hearder-box"},ia={class:"mode-box"},da=["onClick"],pa={key:0},ma={key:1},ca={key:2},_a={class:"upload-video-box"},fa=S(()=>d("p",{class:"el-upload__tip"},"请上传MP4文件并且大小不超过100M的视频文件",-1)),ya={key:0,width:"220",height:"160",controls:""},ga=["src"],ba=["src"],va={class:"upload-img-box"},ha=["src"],Va={key:1,class:"upload-btn"},wa=["onClick"],Ta=S(()=>d("div",{class:"el-upload__tip",style:{"margin-top":"3px"}},"请上传4:3比例的图片 如：220x165",-1)),ka={class:"upload-img-box"},xa=["src"],Ca={key:1,class:"upload-btn"},ja=["onClick"],Ea=S(()=>d("div",{class:"el-upload__tip",style:{"margin-top":"3px"}},"请上传1:1比例的图片 如：90x90",-1)),qa=xe({__name:"expoBannerView",setup(q){const k=je(),y=m(!1),w=m(),ae=m(),U=V([{id:0,type:1,name:"议题轮播"},{id:1,type:2,name:"嘉宾轮播"},{id:2,type:3,name:"推荐企业"}]),te=V([{id:0,type:1,name:"不跳转"},{id:1,type:2,name:"跳转常见问题"},{id:1,type:3,name:"播放视频"}]),N=m(0),g=V({zhanhuiGuid:"",bannerType:1,pageSize:10,page:1}),le=s=>{g.bannerType=s,T()};let b=V({zhanhuiGuid:"",bannerType:1,title:""}),n=V({merchantGuid:"",zhanhuiGuid:"",bannerType:1,title:"",content:"",image:"",jumpType:1,jumpValue:"",sort:1});const D=m("ADD"),v=m(!1),x=m(!1);let P=m([]);const T=async()=>{y.value=!0;let s=await Ee(g);y.value=!1,N.value=s.data.total,P.value=s.data.data},oe=async s=>{g.page=s,T()},ne=V({bannerType:[{required:!0,message:"请选择轮播类型",trigger:"change"}],title:[{required:!0,message:"请输入标题",trigger:"blur"}],content:[{required:!0,message:"请选择轮播内容",trigger:"blur"}],image:[{required:!0,message:"请上传轮播图",trigger:"change"}],jumpType:[{required:!0,message:"请选择跳转类型",trigger:"blur"}],jumpValue:[{required:!0,message:"请输入跳转值",trigger:"blur"}]}),se=()=>{v.value=!0,D.value="ADD"},re=s=>{v.value=!0,D.value="EDIT",w.value&&w.value.resetFields(),n=Object.assign(n,s)},ue=async s=>{try{let o=s.guid;y.value=!0,await Ue({guid:o}),y.value=!1,T(),_.success("删除成功")}catch(o){y.value=!1,_.success(o)}},$=()=>{n.image=""},O=s=>{let o=["image/jpg","image/png","image/jpeg"];if(!o.includes(s.type))return _.warning("当前图片仅支持格式为："+o.join(" ，")),!1},H=async s=>{const o=new FormData;o.append("img",s.file);let u=await Le(o);n.image=u.data},ie=s=>s.size/1024/1024<100?!0:(_.warning("上传文件大小不能超过 100M"),!1),de=async s=>{if(s!=null&&s.file.type.startsWith("video/")){const o=new FormData;o.append("video",s.file);let u=await Me(o);n.jumpValue="",R(()=>{n.jumpValue=u.data})}else alert("请选择一个视频文件！")},pe=s=>{s.validate(async o=>{if(o)try{D.value==="ADD"?(await Ge(n),_.success("新增成功"),R(()=>{v.value=!1,s.resetFields()}),T()):D.value==="EDIT"&&(await Ae(n),_.success("修改成功"),R(()=>{v.value=!1,s.resetFields()}),T())}catch(u){throw _.error(u),new Error(u)}})},me=()=>{x.value=!0},ce=async()=>{try{await Be(b),_.success("修改成功"),x.value=!1}catch(s){throw _.error(s),new Error(s)}},_e=()=>{w.value&&w.value.resetFields()},J=V({zhanhuiGuid:"",position:2,pageSize:200,page:1});let Q=m([]);const fe=async()=>{y.value=!0;let s=await qe(J);Q.value=s.data.data};return Ce(()=>{g.zhanhuiGuid=k.query.guid,n.zhanhuiGuid=k.query.guid,b.zhanhuiGuid=k.query.guid,J.zhanhuiGuid=k.query.guid,n.merchantGuid=k.query.merchantGuid,T(),fe()}),(s,o)=>{const u=Re,h=Se,L=Ne,C=Pe,ye=$e,ge=Oe,be=He,ve=Je,he=Qe,Ve=Ke,we=De,F=We,z=Ye,M=Ze,G=Xe,K=sa,I=ea,W=ra,Te=aa,Y=Fe,ke=ta;return r(),i("div",null,[e(we,{class:"wrapper"},{default:t(()=>[B((r(),p(ve,null,{default:t(()=>[d("div",ua,[e(L,{inline:!0,model:l(g),class:"demo-form-inline"},{default:t(()=>[e(u,{label:"轮播图类型"},{default:t(()=>[d("div",ia,[(r(!0),i(j,null,E(l(U),(a,A)=>(r(),i("div",{class:ze(["item",{active:l(g).bannerType===a.type}]),key:A,onClick:Da=>le(a.type)},Ie(a.name),11,da))),128))])]),_:1}),e(u,null,{default:t(()=>[e(h,{type:"primary",onClick:se},{default:t(()=>[f("新增")]),_:1})]),_:1}),e(u,null,{default:t(()=>[e(h,{type:"primary",onClick:me},{default:t(()=>[f("修改轮播标题")]),_:1})]),_:1})]),_:1},8,["model"])]),e(be,{data:l(P),border:"",style:{width:"100%"}},{default:t(()=>[e(C,{prop:"sysId",label:"sysId",width:"80"}),e(C,{prop:"title",label:"轮播标题"}),e(C,{prop:"image",label:"轮播图片"},{default:t(a=>[e(ye,{style:{width:"60px",height:"60px"},src:a.row.image},null,8,["src"])]),_:1}),e(C,{prop:"jumpType",label:"跳转类型"},{default:t(a=>[a.row.jumpType==1?(r(),i("span",pa,"不跳转")):c("",!0),a.row.jumpType==2?(r(),i("span",ma,"跳转常见问题")):c("",!0),a.row.jumpType==3?(r(),i("span",ca,"播放视频")):c("",!0)]),_:1}),e(C,{label:"操作"},{default:t(a=>[e(h,{size:"small",type:"primary",onClick:A=>re(a.row)},{default:t(()=>[f("编辑")]),_:2},1032,["onClick"]),e(ge,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该问题",onConfirm:A=>ue(a.row)},{reference:t(()=>[e(h,{size:"small",type:"danger"},{default:t(()=>[f("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])]),_:1})),[[ke,l(y)]]),e(Ve,null,{default:t(()=>[e(he,{background:"",layout:"prev,pager, next",total:l(N),"current-page":l(g).page,onCurrentChange:oe},null,8,["total","current-page"])]),_:1})]),_:1}),e(Y,{modelValue:l(v),"onUpdate:modelValue":o[7]||(o[7]=a=>Z(v)?v.value=a:null),title:"创建/编辑轮播",width:"1000px",onClose:o[8]||(o[8]=a=>_e())},{default:t(()=>[e(L,{ref_key:"addForm",ref:w,model:l(n),class:"demo-form-inline","label-width":"120px",rules:l(ne)},{default:t(()=>[e(u,{label:"轮播图类型",prop:"bannerType"},{default:t(()=>[e(z,{modelValue:l(n).bannerType,"onUpdate:modelValue":o[0]||(o[0]=a=>l(n).bannerType=a),placeholder:"请选择"},{default:t(()=>[(r(!0),i(j,null,E(l(U),a=>(r(),p(F,{label:a.name,value:a.type,key:a.type},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"跳转类型",prop:"jumpType"},{default:t(()=>[e(z,{modelValue:l(n).jumpType,"onUpdate:modelValue":o[1]||(o[1]=a=>l(n).jumpType=a),placeholder:"请选择"},{default:t(()=>[(r(!0),i(j,null,E(l(te),a=>(r(),p(F,{label:a.name,value:a.type,key:a.type},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"轮播标题",prop:"title"},{default:t(()=>[e(M,{modelValue:l(n).title,"onUpdate:modelValue":o[2]||(o[2]=a=>l(n).title=a),placeholder:"请输入轮播标题"},null,8,["modelValue"])]),_:1}),l(n).bannerType===2||l(n).bannerType===3?(r(),p(u,{key:0,label:"轮播图内容",prop:"content"},{default:t(()=>[e(M,{modelValue:l(n).content,"onUpdate:modelValue":o[3]||(o[3]=a=>l(n).content=a),placeholder:"轮播图内容"},null,8,["modelValue"])]),_:1})):c("",!0),l(n).jumpType===2?(r(),p(u,{key:1,label:"跳转值",prop:"jumpValue"},{default:t(()=>[e(z,{modelValue:l(n).jumpValue,"onUpdate:modelValue":o[4]||(o[4]=a=>l(n).jumpValue=a),placeholder:"请选择"},{default:t(()=>[(r(!0),i(j,null,E(l(Q),a=>(r(),p(F,{label:a.question,value:a.guid,key:a.guid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):c("",!0),l(n).jumpType===3?(r(),p(u,{key:2,label:"跳转值",prop:"jumpValue"},{default:t(()=>[e(G,{ref:"uploadImgRef",class:"avatar-uploader","before-upload":ie,"show-file-list":!1,"http-request":de},{default:t(()=>[d("div",_a,[e(h,{size:"small",type:"primary"},{default:t(()=>[f("选择视频文件")]),_:1}),fa,l(n).jumpValue?(r(),i("video",ya,[d("source",{src:l(n).jumpValue,type:"video/mp4"},null,8,ga),d("source",{src:l(n).jumpValue,type:"video/ogg"},null,8,ba),f(" 您的浏览器不支持 HTML5 video 标签。 ")])):c("",!0)])]),_:1},512)]),_:1})):c("",!0),l(n).bannerType===1?(r(),p(u,{key:3,label:"轮播图",prop:"image"},{default:t(()=>[e(G,{ref:"uploadImgRef",class:"avatar-uploader","before-upload":O,"show-file-list":!1,"http-request":H},{tip:t(()=>[Ta]),default:t(()=>[d("div",va,[l(n).image?(r(),i("img",{key:0,src:l(n).image,class:"preview-img"},null,8,ha)):(r(),i("div",Va,[e(I,{size:"30",color:"#cdd0d6"},{default:t(()=>[e(K)]),_:1})])),B(d("div",{class:"operate-box",onClick:ee($,["stop"])},[e(I,{size:"22",color:"#ffffff"},{default:t(()=>[e(W)]),_:1})],8,wa),[[X,l(n).image]])])]),_:1},512)]),_:1})):c("",!0),l(n).bannerType===2||l(n).bannerType===3?(r(),p(u,{key:4,label:"嘉宾轮播图",prop:"image"},{default:t(()=>[e(G,{ref:"uploadLogoRef",class:"avatar-uploader","before-upload":O,"show-file-list":!1,"http-request":H},{tip:t(()=>[Ea]),default:t(()=>[d("div",ka,[l(n).image?(r(),i("img",{key:0,src:l(n).image,class:"preview-img"},null,8,xa)):(r(),i("div",Ca,[e(I,{size:"30",color:"#cdd0d6"},{default:t(()=>[e(K)]),_:1})])),B(d("div",{class:"operate-box",onClick:ee($,["stop"])},[e(I,{size:"22",color:"#ffffff"},{default:t(()=>[e(W)]),_:1})],8,ja),[[X,l(n).image]])])]),_:1},512)]),_:1})):c("",!0),e(u,{label:"排序",prop:"sort"},{default:t(()=>[e(Te,{modelValue:l(n).sort,"onUpdate:modelValue":o[5]||(o[5]=a=>l(n).sort=a),min:1},null,8,["modelValue"])]),_:1}),e(u,null,{default:t(()=>[e(h,{type:"primary",onClick:o[6]||(o[6]=a=>pe(l(w)))},{default:t(()=>[f("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(Y,{modelValue:l(x),"onUpdate:modelValue":o[11]||(o[11]=a=>Z(x)?x.value=a:null),title:"自定义轮播标题",width:"600px"},{default:t(()=>[e(L,{ref_key:"bannerForm",ref:ae,model:l(b),class:"demo-form-inline","label-width":"120px"},{default:t(()=>[e(u,{label:"轮播图类型",prop:"bannerType"},{default:t(()=>[e(z,{modelValue:l(b).bannerType,"onUpdate:modelValue":o[9]||(o[9]=a=>l(b).bannerType=a),placeholder:"请选择"},{default:t(()=>[(r(!0),i(j,null,E(l(U),a=>(r(),p(F,{label:a.name,value:a.type,key:a.type},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"轮播标题",prop:"title"},{default:t(()=>[e(M,{modelValue:l(b).title,"onUpdate:modelValue":o[10]||(o[10]=a=>l(b).title=a),placeholder:"请输入轮播标题"},null,8,["modelValue"])]),_:1}),e(u,null,{default:t(()=>[e(h,{type:"primary",onClick:ce},{default:t(()=>[f("提交")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const Qa=na(qa,[["__scopeId","data-v-21cdb017"]]);export{Qa as default};
