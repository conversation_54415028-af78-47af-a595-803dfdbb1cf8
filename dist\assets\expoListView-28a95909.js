import{d as M,a5 as H,r as h,a as x,a6 as O,c as g,b as e,w as a,bs as Q,e as U,u as Y,o as _,T as Z,h as r,I as j,f as m,i as n,G as J,H as K,aC as W,U as l,bt as X,E as ee,p as te,l as ae,q as oe,s as ne,af as se,v as le,y as ie,x as re,Y as ce,Z as de,_ as ue}from"./index-5837f9dc.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                     */const pe={class:"hearder-box"},_e={class:"mode-box"},me=["onClick"],fe={style:{color:"#ccc","margin-left":"10px"}},he={style:{"margin-top":"5px",color:"#ccc"}},ge=M({__name:"expoListView",setup(we){const C=H(),u=Y(),w=h(0),f=h(!1),i=x({merchantGuid:"",showStatus:!0,pageSize:10,page:1});let E=O(()=>C.getTenantInfo.guid),b=h([]);const p=async()=>{f.value=!0,i.merchantGuid=E.value;let o=await Q(i);f.value=!1,w.value=o.data.total,b.value=o.data.data},k=x([{id:"",name:"全部"},{id:!0,name:"展示中"},{id:!1,name:"未展示"}]),S=o=>{i.showStatus=o,p()},L=async o=>{i.page=o,p()},I=o=>{u.push({name:"setExpoInfo",query:{guid:o.guid,type:"EDIT"}})},T=()=>{u.push({name:"setExpoInfo",query:{type:"ADD"}})},q=o=>{u.push({name:"expoFaqList",query:{guid:o.guid,merchantGuid:"e108201b02ae42e686bcc4c302cbbd11"}})},A=o=>{u.push({name:"expoBannerList",query:{guid:o.guid,merchantGuid:"e108201b02ae42e686bcc4c302cbbd11"}})},z=async o=>{let y={guid:o.guid};await X(y),ee.success("修改状态成功"),p()},B=o=>{u.push({name:"expoAdmin",query:{guid:o.guid}})};return p(),(o,y)=>{const c=te,v=ae,D=oe,s=ne,F=se,G=le,P=ie,V=re,$=ce,N=U,R=de;return _(),g("div",null,[e(N,{class:"wrapper"},{default:a(()=>[Z((_(),j(P,null,{default:a(()=>[m("div",pe,[e(D,{inline:!0,model:r(i),class:"demo-form-inline"},{default:a(()=>[e(v,null,{default:a(()=>[e(c,{type:"primary",onClick:T},{default:a(()=>[n("新增")]),_:1})]),_:1}),e(v,{label:"展示状态"},{default:a(()=>[m("div",_e,[(_(!0),g(J,null,K(r(k),(t,d)=>(_(),g("div",{class:W(["item",{active:r(i).showStatus===t.id}]),key:d,onClick:be=>S(t.id)},l(t.name),11,me))),128))])]),_:1})]),_:1},8,["model"])]),e(G,{data:r(b),border:"",style:{width:"100%"}},{default:a(()=>[e(s,{prop:"sysId",label:"sysId",width:"80"}),e(s,{prop:"showOrder",label:"排序",width:"100"}),e(s,{prop:"name",label:"展会名称",width:"200"}),e(s,{prop:"startTime",label:"展会开始时间/截止时间",width:"180"},{default:a(t=>[n(l(t.row.startTime)+" - "+l(t.row.endTime),1)]),_:1}),e(s,{prop:"createTime",label:"展会创建时间",width:"200"}),e(s,{prop:"aiPoint",label:"AI点数余额",width:"260"},{default:a(t=>[n(l(t.row.aiPoint)+" ",1),m("span",fe,l(t.row.aiPoint<200?"点数不足，请及时充值":""),1)]),_:1}),e(s,{prop:"showStatus",label:"展示状态",width:"160"},{default:a(t=>[n(l(t.row.showStatus?"展示中":"未展示")+" ",1),m("p",he,l(t.row.closeReason),1)]),_:1}),e(s,{fixed:"right",label:"操作","min-width":"350"},{default:a(t=>[e(c,{size:"small",type:"primary",onClick:d=>I(t.row)},{default:a(()=>[n("编辑")]),_:2},1032,["onClick"]),e(F,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:t.row.status==2?"是否启用展会":"是否禁用展会",onConfirm:d=>z(t.row)},{reference:a(()=>[e(c,{size:"small",type:t.row.status==2?"success":"danger"},{default:a(()=>[n(l(t.row.status==2?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:2},1032,["title","onConfirm"]),e(c,{size:"small",type:"primary",onClick:d=>q(t.row)},{default:a(()=>[n("常见问题设置")]),_:2},1032,["onClick"]),e(c,{size:"small",type:"primary",onClick:d=>A(t.row)},{default:a(()=>[n("轮播图设置")]),_:2},1032,["onClick"]),e(c,{size:"small",type:"primary",onClick:d=>B(t.row)},{default:a(()=>[n("管理员设置")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[R,r(f)]]),e($,null,{default:a(()=>[e(V,{background:"",layout:"prev,pager, next",total:r(w),"current-page":r(i).page,onCurrentChange:L},null,8,["total","current-page"])]),_:1})]),_:1})])}}});const Te=ue(ge,[["__scopeId","data-v-662c2b1b"]]);export{Te as default};
