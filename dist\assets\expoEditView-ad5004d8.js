import{d as W,a as b,r as g,bu as Y,c as u,b as o,w as r,D as Z,e as ee,o as i,h as t,f as m,T as q,ah as U,ai as L,G as oe,H as le,I as te,i as ae,E as _,J as D,bv as re,ax as se,bw as ne,k as de,l as ie,a3 as pe,O as ue,P as me,X as ce,t as ge,m as _e,n as fe,p as we,q as ve,y as he,bb as be,bc as Ve,bx as xe,az as ye,_ as Te}from"./index-2d10794a.js";/* empty css                     *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                       *//* empty css                  *//* empty css                    */import{_ as ke,a as Ee}from"./plus-25c99156.js";/* empty css                        */const P=f=>(be("data-v-8aec2950"),f=f(),Ve(),f),Ie={class:"upload-img-box"},qe=["src"],Ue={key:1,class:"upload-btn"},Le={class:"operate-box"},De=P(()=>m("div",{class:"el-upload__tip",style:{"margin-top":"3px"}},"上传尺寸为：90px * 90px的图片",-1)),Pe={class:"upload-img-box"},Re=["src"],Ce={key:1,class:"upload-btn"},Se={class:"operate-box"},Oe=P(()=>m("div",{class:"el-upload__tip",style:{"margin-top":"3px"}},"上传尺寸为：550px * 310px",-1)),Ae=W({__name:"expoEditView",setup(f){const p=Z();let l=b({merchantGuid:"",name:"",shortName:"",logo:"",slogo:"",cover:"",showOrder:1,description:"",startTime:"2024-09-01",endTime:"2024-10-01",showTime:1,endIsShow:1,welcomeText:"欢迎来到 2024年设宇宙商协通大会 !我是您的智能助手。 请问有什么可以帮助您的吗",isRequirePhone:2,aiChatPrompt:"请你作为展会助手AI，为用户提供热情、专业、耐心的服务。行为准则：友好热情： 以积极的态度回应用户，使用礼貌和鼓励性的语言。专业准确： 提供的信息需要准确无误，确保用户获得可靠的展会信息。耐心细致： 对于用户的问题，即使重复或复杂，也要耐心解答，不急躁。主动提供帮助： 在用户提问时，除了回答问题，还可以主动提供额外相关信息或建议。",merchantKnowledgeGuid:"",knowledgePrompt:"本次用户提问有匹配的知识库内容，请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是："});const v=g("ADD"),V=g(),R=b({name:[{required:!0,message:"请输入展会名称",trigger:"blur"}],shortName:[{required:!0,message:"请输入展会简称",trigger:"blur"}],logo:[{required:!0,message:"请上传Logo",trigger:"change"}],slogo:[{required:!0,message:"请输入展会标语",trigger:"blur"}],description:[{required:!0,message:"请输入展会标语",trigger:"blur"}],cover:[{required:!0,message:"请上传展会封面",trigger:"change"}],nadescriptionme:[{required:!0,message:"请输入展会描述",trigger:"blur"}],startTime:[{required:!0,message:"请选择展会开始时间",trigger:"change"}],endTime:[{required:!0,message:"请选择展会结束时间",trigger:"change"}]}),x=b({merchantGuid:"",pageSize:200,page:1}),C=g(0),y=g([]),S=async()=>{let n=await ye(x);y.value=n.data.data,C.value=n.data.total},O=g(),A=n=>{let e=["image/jpg","image/png","image/jpeg"];return new Promise((d,s)=>{const c=new Image;if(c.src=URL.createObjectURL(n),!e.includes(n.type)){_.warning("当前图片仅支持格式为："+e.join(" ，")),s(!1);return}d(!0)})},G=n=>{let e=["image/jpg","image/png","image/jpeg"];return new Promise((d,s)=>{const c=new Image;if(c.src=URL.createObjectURL(n),!e.includes(n.type)){_.warning("当前图片仅支持格式为："+e.join(" ，")),s(!1);return}d(!0)})},j=async n=>{const e=new FormData;e.append("img",n.file);let d=await D(e);l.logo=d.data},N=()=>{l.logo=""},F=async n=>{const e=new FormData;e.append("img",n.file);let d=await D(e);l.cover=d.data},z=()=>{l.cover=""},K=async n=>{n.validate(async e=>{if(e)try{v.value==="ADD"?(await re(l),_.success("新增成功"),se(()=>{n.resetFields()})):v.value==="EDIT"&&(await ne(l),_.success("修改成功"))}catch(d){throw _.error(d),new Error(d)}})},B=async()=>{if(p.query.type==="EDIT"&&p.query.guid){let n={guid:p.query.guid},e=await xe(n);l=Object.assign(l,e.data),x.merchantGuid=e.data.merchantGuid,S()}};return v.value=p.query.type,Y(()=>p.query.guid,n=>{n&&p.query.type==="EDIT"&&B()},{immediate:!1}),(n,e)=>{const d=de,s=ie,c=pe,T=ke,w=ue,k=Ee,E=me,I=ce,h=ge,M=_e,H=fe,J=we,X=ve,$=he,Q=ee;return i(),u("div",null,[o(Q,{class:"wrapper"},{default:r(()=>[o($,null,{default:r(()=>[o(X,{ref_key:"addForm",ref:V,model:t(l),rules:t(R),class:"form-box","label-width":"200px"},{default:r(()=>[o(s,{label:"展会名称",prop:"name"},{default:r(()=>[o(d,{class:"input",modelValue:t(l).name,"onUpdate:modelValue":e[0]||(e[0]=a=>t(l).name=a),placeholder:"展会名称不要超过20字",maxlength:"20"},null,8,["modelValue"])]),_:1}),o(s,{label:"展会简称",prop:"shortName"},{default:r(()=>[o(d,{class:"input",modelValue:t(l).shortName,"onUpdate:modelValue":e[1]||(e[1]=a=>t(l).shortName=a),placeholder:"展会简称不要写太长(不然C端不好看)"},null,8,["modelValue"])]),_:1}),o(s,{label:"排序",prop:"showOrder"},{default:r(()=>[o(c,{modelValue:t(l).showOrder,"onUpdate:modelValue":e[2]||(e[2]=a=>t(l).showOrder=a),min:1},null,8,["modelValue"])]),_:1}),o(s,{label:"主办方LOGO",prop:"logo"},{default:r(()=>[o(E,{ref_key:"uploadImgRef",ref:O,class:"avatar-uploader","before-upload":A,"show-file-list":!1,"http-request":j},{tip:r(()=>[De]),default:r(()=>[m("div",Ie,[t(l).logo?(i(),u("img",{key:0,src:t(l).logo,class:"preview-img"},null,8,qe)):(i(),u("div",Ue,[o(w,{size:"30",color:"#cdd0d6"},{default:r(()=>[o(T)]),_:1})])),q(m("div",Le,[o(w,{size:"22",color:"#ffffff",onClick:L(N,["stop"])},{default:r(()=>[o(k)]),_:1},8,["onClick"])],512),[[U,t(l).logo]])])]),_:1},512)]),_:1}),o(s,{label:"展会封面图片",prop:"cover"},{default:r(()=>[o(E,{ref:"uploadImgRef2",class:"avatar-uploader","before-upload":G,"show-file-list":!1,"http-request":F},{tip:r(()=>[Oe]),default:r(()=>[m("div",Pe,[t(l).cover?(i(),u("img",{key:0,src:t(l).cover,class:"preview-img"},null,8,Re)):(i(),u("div",Ce,[o(w,{size:"30",color:"#cdd0d6"},{default:r(()=>[o(T)]),_:1})])),q(m("div",Se,[o(w,{size:"22",color:"#ffffff",onClick:L(z,["stop"])},{default:r(()=>[o(k)]),_:1},8,["onClick"])],512),[[U,t(l).cover]])])]),_:1},512)]),_:1}),o(s,{label:"展会标语",prop:"slogo"},{default:r(()=>[o(d,{modelValue:t(l).slogo,"onUpdate:modelValue":e[3]||(e[3]=a=>t(l).slogo=a),placeholder:"展会标语不要超过10字",maxlength:"20"},null,8,["modelValue"])]),_:1}),o(s,{label:"展会简介",prop:"description"},{default:r(()=>[o(d,{modelValue:t(l).description,"onUpdate:modelValue":e[4]||(e[4]=a=>t(l).description=a),type:"textarea",rows:3,placeholder:"展会简介不要超过100字",maxlength:"100"},null,8,["modelValue"])]),_:1}),o(s,{label:"展会开始时间",prop:"startTime"},{default:r(()=>[o(I,{modelValue:t(l).startTime,"onUpdate:modelValue":e[5]||(e[5]=a=>t(l).startTime=a),type:"date",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),o(s,{label:"展会结束时间",prop:"endTime"},{default:r(()=>[o(I,{modelValue:t(l).endTime,"onUpdate:modelValue":e[6]||(e[6]=a=>t(l).endTime=a),type:"date",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),o(s,{label:"是否显示展会时间",prop:"showTime"},{default:r(()=>[o(h,{modelValue:t(l).showTime,"onUpdate:modelValue":e[7]||(e[7]=a=>t(l).showTime=a),"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue"])]),_:1}),o(s,{label:"展会结束是否自动关闭展会",prop:"endIsShow"},{default:r(()=>[o(h,{modelValue:t(l).endIsShow,"onUpdate:modelValue":e[8]||(e[8]=a=>t(l).endIsShow=a),"active-value":1,"inactive-value":2,"active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue"])]),_:1}),o(s,{label:"是否强制校验手机号码",prop:"isRequirePhone"},{default:r(()=>[o(h,{modelValue:t(l).isRequirePhone,"onUpdate:modelValue":e[9]||(e[9]=a=>t(l).isRequirePhone=a),"active-value":1,"inactive-value":2,"active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue"])]),_:1}),o(s,{label:"AI助理对话欢迎语",prop:"welcomeText"},{default:r(()=>[o(d,{modelValue:t(l).welcomeText,"onUpdate:modelValue":e[10]||(e[10]=a=>t(l).welcomeText=a),type:"textarea",rows:3,placeholder:"欢迎语不要超过100字",maxlength:"100"},null,8,["modelValue"])]),_:1}),o(s,{label:"AI对话提示词",prop:"aiChatPrompt"},{default:r(()=>[o(d,{modelValue:t(l).aiChatPrompt,"onUpdate:modelValue":e[11]||(e[11]=a=>t(l).aiChatPrompt=a),type:"textarea",rows:5,placeholder:"AI对话提示词"},null,8,["modelValue"])]),_:1}),o(s,{label:"选择知识库",prop:"merchantKnowledgeGuid"},{default:r(()=>[o(H,{modelValue:t(l).merchantKnowledgeGuid,"onUpdate:modelValue":e[12]||(e[12]=a=>t(l).merchantKnowledgeGuid=a),placeholder:"请选择"},{default:r(()=>[(i(!0),u(oe,null,le(t(y),a=>(i(),te(M,{label:a.knowledgeTitle,value:a.guid,key:a.guid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(s,{label:"知识库引用提示词",prop:"knowledgePrompt"},{default:r(()=>[o(d,{modelValue:t(l).knowledgePrompt,"onUpdate:modelValue":e[13]||(e[13]=a=>t(l).knowledgePrompt=a),type:"textarea",rows:3,placeholder:"知识库引用提示词"},null,8,["modelValue"])]),_:1}),o(s,null,{default:r(()=>[o(J,{type:"primary",onClick:e[14]||(e[14]=a=>K(t(V)))},{default:r(()=>[ae("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1})])}}});const $e=Te(Ae,[["__scopeId","data-v-8aec2950"]]);export{$e as default};
