import{d as O,r as g,a6 as P,c as V,b as e,w as t,cc as Q,E as x,e as W,o as f,T as X,h as l,I as U,f as s,i as d,A,b4 as Y,G as ee,H as te,U as ae,cd as le,p as ie,s as ne,v as oe,ce as se,cf as re,a3 as _e,l as de,b8 as ue,ba as pe,t as me,k as ce,ae as ge,b6 as fe,q as ye,C as we,y as he,Z as ve,bb as be,bc as Ve,_ as xe}from"./index-2d10794a.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                *//* empty css               *//* empty css                 *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                   *//* empty css                        */const u=v=>(be("data-v-17465eb9"),v=v(),Ve(),v),ke={class:"card-header"},ze=u(()=>s("span",null,"平台配置管理",-1)),Ee=u(()=>s("span",{class:"unit"},"%",-1)),Ce=u(()=>s("span",{class:"unit"},"%",-1)),je=u(()=>s("span",{class:"unit"},"%",-1)),Ie=u(()=>s("span",{class:"unit"},"元",-1)),Ue=u(()=>s("span",{class:"unit"},"个",-1)),Ae=u(()=>s("span",{class:"unit"},"个",-1)),qe=u(()=>s("span",{class:"unit"},"元",-1)),De=u(()=>s("span",{class:"unit"},"元",-1)),Te=u(()=>s("span",{class:"unit"},"点/秒",-1)),Se={class:"model-config"},Be={class:"model-input"},Le={key:0,class:"model-list"},Me={key:1,class:"empty-models"},Ne={class:"dialog-footer"},Re=O({__name:"ConfigListView",setup(v){const k=g(!1),z=g(!1);let a=g({platform_fee_rate:0,commission_rate:0,auto_audit_enabled:!1,daily_create_limit:0,total_create_limit:0,max_agent_price:0,daily_withdraw_limit:0,single_withdraw_limit:0,withdraw_fee_rate:0,supported_ai_models:[],shuziren_chanjing_appid:"",shuziren_chanjing_key:"",shuziren_points_per_second:0});const y=g(!1),q=P(()=>[{category:"费率配置",name:"平台手续费比例",value:a.value.platform_fee_rate,unit:"%",description:"平台收取的手续费比例"},{category:"费率配置",name:"邀请佣金比例",value:a.value.commission_rate,unit:"%",description:"邀请用户获得的佣金比例"},{category:"费率配置",name:"提现手续费比例",value:a.value.withdraw_fee_rate,unit:"%",description:"用户提现时收取的手续费比例"},{category:"智能体配置",name:"自动审批开关",value:a.value.auto_audit_enabled?"开启":"关闭",unit:"",description:"智能体提交后是否自动审批通过"},{category:"智能体配置",name:"最大收费金额",value:a.value.max_agent_price,unit:"元",description:"智能体可设置的最大收费金额"},{category:"智能体配置",name:"单日创建限制",value:a.value.daily_create_limit,unit:"个",description:"用户每天最多可创建的智能体数量"},{category:"智能体配置",name:"累计创建限制",value:a.value.total_create_limit,unit:"个",description:"用户累计最多可创建的智能体数量"},{category:"提现配置",name:"单日提现限额",value:a.value.daily_withdraw_limit,unit:"元",description:"用户每天最多可提现的金额"},{category:"提现配置",name:"单笔提现限额",value:a.value.single_withdraw_limit,unit:"元",description:"用户单次提现的最大金额"},{category:"AI模型配置",name:"支持的AI模型",value:a.value.supported_ai_models.length>0?a.value.supported_ai_models.join(", "):"暂无配置",unit:"",description:"平台支持的AI模型列表"},{category:"数字人配置",name:"蝉镜数字人appid",value:a.value.shuziren_chanjing_appid,unit:"",description:"蝉镜数字人appid"},{category:"数字人配置",name:"蝉镜数字人key",value:a.value.shuziren_chanjing_key,unit:"",description:"蝉镜数字人key"},{category:"数字人配置",name:"蝉镜数字人每秒点数",value:a.value.shuziren_points_per_second,unit:"点/秒",description:"蝉镜数字人每秒消耗的点数"}]),E=g(),D={platform_fee_rate:[{required:!0,message:"请输入平台手续费比例",trigger:"blur"},{type:"number",min:0,max:100,message:"比例范围为0-100",trigger:"blur"}],commission_rate:[{required:!0,message:"请输入邀请佣金比例",trigger:"blur"},{type:"number",min:0,max:100,message:"比例范围为0-100",trigger:"blur"}],daily_create_limit:[{required:!0,message:"请输入单日创建限制",trigger:"blur"},{type:"number",min:1,message:"最少为1个",trigger:"blur"}],total_create_limit:[{required:!0,message:"请输入累计创建限制",trigger:"blur"},{type:"number",min:1,message:"最少为1个",trigger:"blur"}],max_agent_price:[{required:!0,message:"请输入最大收费金额",trigger:"blur"},{type:"number",min:0,message:"金额不能为负数",trigger:"blur"}],daily_withdraw_limit:[{required:!0,message:"请输入单日提现限额",trigger:"blur"},{type:"number",min:0,message:"金额不能为负数",trigger:"blur"}],single_withdraw_limit:[{required:!0,message:"请输入单笔提现限额",trigger:"blur"},{type:"number",min:0,message:"金额不能为负数",trigger:"blur"}],withdraw_fee_rate:[{required:!0,message:"请输入提现手续费比例",trigger:"blur"},{type:"number",min:0,max:100,message:"比例范围为0-100",trigger:"blur"}]},T=async()=>{k.value=!0;try{let o=await Q({merchantGuid:""});o.data&&(a.value={platform_fee_rate:o.data.platform_fee_rate||0,commission_rate:o.data.commission_rate||0,auto_audit_enabled:o.data.auto_audit_enabled||!1,daily_create_limit:o.data.daily_create_limit||0,total_create_limit:o.data.total_create_limit||0,max_agent_price:o.data.max_agent_price||0,daily_withdraw_limit:o.data.daily_withdraw_limit||0,single_withdraw_limit:o.data.single_withdraw_limit||0,withdraw_fee_rate:o.data.withdraw_fee_rate||0,supported_ai_models:o.data.supported_ai_models||[],shuziren_chanjing_appid:o.data.shuziren_chanjing_appid||"",shuziren_chanjing_key:o.data.shuziren_chanjing_key||"",shuziren_points_per_second:o.data.shuziren_points_per_second||0})}catch{x.error("获取配置数据失败")}finally{k.value=!1}},S=()=>{y.value=!0},B=async()=>{E.value&&await E.value.validate(async o=>{if(o){z.value=!0;try{await le({merchantGuid:"",configs:a.value}),x.success("配置保存成功")}catch{x.error("配置保存失败")}finally{z.value=!1}}})},m=g(""),j=()=>{m.value.trim()&&(a.value.supported_ai_models.includes(m.value.trim())?x.warning("该模型已存在"):(a.value.supported_ai_models.push(m.value.trim()),m.value=""))},L=o=>{a.value.supported_ai_models.splice(o,1)};return T(),(o,i)=>{const b=ie,w=ne,M=oe,N=se,h=re,p=_e,r=de,_=ue,c=pe,R=me,C=ce,F=ge,G=fe,K=ye,$=we,H=he,Z=W,J=ve;return f(),V("div",null,[e(Z,{class:"wrapper"},{default:t(()=>[e(H,null,{default:t(()=>[X((f(),U(N,{class:"config-card"},{header:t(()=>[s("div",ke,[ze,e(b,{type:"primary",onClick:S},{default:t(()=>[d(" 编辑配置 ")]),_:1})])]),default:t(()=>[e(M,{data:l(q),border:"",style:{width:"100%"}},{default:t(()=>[e(w,{prop:"category",label:"配置分类",width:"150"}),e(w,{prop:"name",label:"配置项",width:"200"}),e(w,{prop:"value",label:"当前值"}),e(w,{prop:"unit",label:"单位",width:"80"}),e(w,{prop:"description",label:"说明","show-overflow-tooltip":""})]),_:1},8,["data"])]),_:1})),[[J,l(k)]]),e($,{title:"编辑平台配置",modelValue:l(y),"onUpdate:modelValue":i[14]||(i[14]=n=>A(y)?y.value=n:null),width:"800px"},{footer:t(()=>[s("span",Ne,[e(b,{onClick:i[13]||(i[13]=n=>y.value=!1)},{default:t(()=>[d("取消")]),_:1}),e(b,{type:"primary",onClick:B,loading:l(z)},{default:t(()=>[d("保存")]),_:1},8,["loading"])])]),default:t(()=>[e(K,{ref_key:"formRef",ref:E,model:l(a),rules:D,"label-width":"150px",class:"config-form"},{default:t(()=>[e(h,{"content-position":"left"},{default:t(()=>[d("费率配置")]),_:1}),e(c,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(r,{label:"平台手续费比例",prop:"platform_fee_rate"},{default:t(()=>[e(p,{modelValue:l(a).platform_fee_rate,"onUpdate:modelValue":i[0]||(i[0]=n=>l(a).platform_fee_rate=n),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Ee]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(r,{label:"邀请佣金比例",prop:"commission_rate"},{default:t(()=>[e(p,{modelValue:l(a).commission_rate,"onUpdate:modelValue":i[1]||(i[1]=n=>l(a).commission_rate=n),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Ce]),_:1})]),_:1})]),_:1}),e(c,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(r,{label:"提现手续费比例",prop:"withdraw_fee_rate"},{default:t(()=>[e(p,{modelValue:l(a).withdraw_fee_rate,"onUpdate:modelValue":i[2]||(i[2]=n=>l(a).withdraw_fee_rate=n),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),je]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("智能体配置")]),_:1}),e(c,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(r,{label:"自动审批开关"},{default:t(()=>[e(R,{modelValue:l(a).auto_audit_enabled,"onUpdate:modelValue":i[3]||(i[3]=n=>l(a).auto_audit_enabled=n),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(r,{label:"最大收费金额",prop:"max_agent_price"},{default:t(()=>[e(p,{modelValue:l(a).max_agent_price,"onUpdate:modelValue":i[4]||(i[4]=n=>l(a).max_agent_price=n),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Ie]),_:1})]),_:1})]),_:1}),e(c,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(r,{label:"单日创建限制",prop:"daily_create_limit"},{default:t(()=>[e(p,{modelValue:l(a).daily_create_limit,"onUpdate:modelValue":i[5]||(i[5]=n=>l(a).daily_create_limit=n),min:1,style:{width:"100%"}},null,8,["modelValue"]),Ue]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(r,{label:"累计创建限制",prop:"total_create_limit"},{default:t(()=>[e(p,{modelValue:l(a).total_create_limit,"onUpdate:modelValue":i[6]||(i[6]=n=>l(a).total_create_limit=n),min:1,style:{width:"100%"}},null,8,["modelValue"]),Ae]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("提现配置")]),_:1}),e(c,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(r,{label:"单日提现限额",prop:"daily_withdraw_limit"},{default:t(()=>[e(p,{modelValue:l(a).daily_withdraw_limit,"onUpdate:modelValue":i[7]||(i[7]=n=>l(a).daily_withdraw_limit=n),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),qe]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(r,{label:"单笔提现限额",prop:"single_withdraw_limit"},{default:t(()=>[e(p,{modelValue:l(a).single_withdraw_limit,"onUpdate:modelValue":i[8]||(i[8]=n=>l(a).single_withdraw_limit=n),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),De]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("蝉镜数字人配置")]),_:1}),e(c,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(r,{label:"蝉镜数字人appid",prop:"shuziren_chanjing_appid"},{default:t(()=>[e(C,{modelValue:l(a).shuziren_chanjing_appid,"onUpdate:modelValue":i[9]||(i[9]=n=>l(a).shuziren_chanjing_appid=n),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(r,{label:"蝉镜数字人key",prop:"shuziren_chanjing_key"},{default:t(()=>[e(C,{modelValue:l(a).shuziren_chanjing_key,"onUpdate:modelValue":i[10]||(i[10]=n=>l(a).shuziren_chanjing_key=n),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(r,{label:"蝉镜数字人每秒点数",prop:"shuziren_points_per_second"},{default:t(()=>[e(p,{modelValue:l(a).shuziren_points_per_second,"onUpdate:modelValue":i[11]||(i[11]=n=>l(a).shuziren_points_per_second=n),min:0,style:{width:"100%"}},null,8,["modelValue"]),Te]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("AI模型配置")]),_:1}),e(r,{label:"支持的AI模型"},{default:t(()=>[s("div",Se,[s("div",Be,[e(C,{modelValue:l(m),"onUpdate:modelValue":i[12]||(i[12]=n=>A(m)?m.value=n:null),placeholder:"请输入AI模型名称",onKeyup:Y(j,["enter"]),style:{width:"300px"}},null,8,["modelValue","onKeyup"]),e(b,{type:"primary",onClick:j,style:{"margin-left":"10px"}},{default:t(()=>[d("添加")]),_:1})]),l(a).supported_ai_models.length>0?(f(),V("div",Le,[(f(!0),V(ee,null,te(l(a).supported_ai_models,(n,I)=>(f(),U(F,{key:I,closable:"",onClose:Fe=>L(I),style:{margin:"5px 5px 0 0"}},{default:t(()=>[d(ae(n),1)]),_:2},1032,["onClose"]))),128))])):(f(),V("div",Me,[e(G,{type:"info"},{default:t(()=>[d("暂无配置的AI模型")]),_:1})]))])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})]),_:1})])}}});const tt=xe(Re,[["__scopeId","data-v-17465eb9"]]);export{tt as default};
